import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kit/src/features/login/logic/examinee_helper.dart';
import 'package:flutter_kit/src/shared/services/storage/in_memory_storage.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';
import 'package:get_it/get_it.dart';

void main() {
  group('ExamineeHelper Tests', () {
    late ExamineeHelper examineeHelper;
    late Storage storage;

    setUp(() async {
      // 清理GetIt实例
      if (GetIt.instance.isRegistered<Storage>()) {
        GetIt.instance.unregister<Storage>();
      }
      
      // 注册内存存储用于测试
      storage = InMemoryStorage();
      await storage.init();
      GetIt.instance.registerSingleton<Storage>(storage);
      
      examineeHelper = ExamineeHelper();
    });

    tearDown(() async {
      await storage.clear();
      GetIt.instance.reset();
    });

    test('should save and retrieve examinee token', () async {
      const String testToken = 'test_token_123';
      
      // 保存token
      final bool saveResult = await examineeHelper.examinee_token(testToken);
      expect(saveResult, isTrue);
      
      // 获取token
      final String? retrievedToken = await examineeHelper.getExamineeToken();
      expect(retrievedToken, equals(testToken));
    });

    test('should return null when no token exists', () async {
      final String? token = await examineeHelper.getExamineeToken();
      expect(token, isNull);
    });

    test('should check login status correctly', () async {
      // 初始状态应该是未登录
      bool isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isFalse);
      
      // 保存token后应该是已登录
      await examineeHelper.examinee_token('test_token');
      isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isTrue);
    });

    test('should save and retrieve user info', () async {
      final Map<String, dynamic> testUserInfo = {
        'id': '123',
        'name': '测试用户',
        'email': '<EMAIL>',
      };
      
      // 保存用户信息
      final bool saveResult = await examineeHelper.saveUserInfo(testUserInfo);
      expect(saveResult, isTrue);
      
      // 获取用户信息
      final Map<String, dynamic>? retrievedInfo = await examineeHelper.getUserInfo();
      expect(retrievedInfo, isNotNull);
      expect(retrievedInfo!['id'], equals('123'));
      expect(retrievedInfo['name'], equals('测试用户'));
      expect(retrievedInfo['email'], equals('<EMAIL>'));
    });

    test('should perform complete login process', () async {
      const String testToken = 'login_token_456';
      final Map<String, dynamic> testUserInfo = {
        'id': '456',
        'name': '登录用户',
        'phone': '13800138000',
      };
      
      // 执行登录
      final bool loginResult = await examineeHelper.login(testToken, userInfo: testUserInfo);
      expect(loginResult, isTrue);
      
      // 验证登录状态
      final bool isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isTrue);
      
      // 验证token
      final String? savedToken = await examineeHelper.getExamineeToken();
      expect(savedToken, equals(testToken));
      
      // 验证用户信息
      final Map<String, dynamic>? savedUserInfo = await examineeHelper.getUserInfo();
      expect(savedUserInfo, isNotNull);
      expect(savedUserInfo!['id'], equals('456'));
      expect(savedUserInfo['name'], equals('登录用户'));
    });

    test('should perform logout correctly', () async {
      // 先登录
      await examineeHelper.login('test_token', userInfo: {'id': '123'});
      
      // 验证已登录
      bool isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isTrue);
      
      // 执行退出登录
      final bool logoutResult = await examineeHelper.logout();
      expect(logoutResult, isTrue);
      
      // 验证已退出登录
      isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isFalse);
      
      // 验证数据已清除
      final String? token = await examineeHelper.getExamineeToken();
      final Map<String, dynamic>? userInfo = await examineeHelper.getUserInfo();
      expect(token, isNull);
      expect(userInfo, isNull);
    });

    test('should refresh user info when logged in', () async {
      // 先登录
      await examineeHelper.login('test_token', userInfo: {
        'id': '123',
        'name': '原始用户',
        'email': '<EMAIL>',
      });
      
      // 刷新用户信息
      final Map<String, dynamic> newUserInfo = {
        'id': '123',
        'name': '更新用户',
        'email': '<EMAIL>',
        'phone': '13900139000',
      };
      
      final bool refreshResult = await examineeHelper.refreshUserInfo(newUserInfo);
      expect(refreshResult, isTrue);
      
      // 验证用户信息已更新
      final Map<String, dynamic>? updatedInfo = await examineeHelper.getUserInfo();
      expect(updatedInfo, isNotNull);
      expect(updatedInfo!['name'], equals('更新用户'));
      expect(updatedInfo['email'], equals('<EMAIL>'));
      expect(updatedInfo['phone'], equals('13900139000'));
    });

    test('should not refresh user info when not logged in', () async {
      final Map<String, dynamic> newUserInfo = {
        'id': '123',
        'name': '测试用户',
      };
      
      // 未登录状态下尝试刷新用户信息
      final bool refreshResult = await examineeHelper.refreshUserInfo(newUserInfo);
      expect(refreshResult, isFalse);
    });

    test('should clear all data', () async {
      // 先保存一些数据
      await examineeHelper.login('test_token', userInfo: {'id': '123'});
      
      // 验证数据存在
      bool isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isTrue);
      
      // 清除所有数据
      await examineeHelper.clearAllData();
      
      // 验证数据已清除
      isLoggedIn = await examineeHelper.isLogin();
      final String? token = await examineeHelper.getExamineeToken();
      final Map<String, dynamic>? userInfo = await examineeHelper.getUserInfo();
      
      expect(isLoggedIn, isFalse);
      expect(token, isNull);
      expect(userInfo, isNull);
    });

    test('should handle empty token correctly', () async {
      // 保存空token
      final bool saveResult = await examineeHelper.examinee_token('');
      expect(saveResult, isTrue);
      
      // 登录状态应该是false（因为token为空）
      final bool isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isFalse);
    });

    test('should login without user info', () async {
      const String testToken = 'token_only';
      
      // 只保存token，不保存用户信息
      final bool loginResult = await examineeHelper.login(testToken);
      expect(loginResult, isTrue);
      
      // 验证登录状态
      final bool isLoggedIn = await examineeHelper.isLogin();
      expect(isLoggedIn, isTrue);
      
      // 验证token
      final String? savedToken = await examineeHelper.getExamineeToken();
      expect(savedToken, equals(testToken));
      
      // 用户信息应该为空
      final Map<String, dynamic>? userInfo = await examineeHelper.getUserInfo();
      expect(userInfo, isNull);
    });
  });
}
