import 'package:flutter_test/flutter_test.dart';
import '../lib/src/shared/utils/md5_utils.dart';

void main() {
  group('MD5Utils Tests', () {
    test('should calculate correct MD5 hash for empty string', () {
      const String input = '';
      const String expected = 'd41d8cd98f00b204e9800998ecf8427e';
      final String result = MD5Utils.hexMD5(input);
      expect(result, equals(expected));
    });

    test('should calculate correct MD5 hash for "a"', () {
      const String input = 'a';
      const String expected = '0cc175b9c0f1b6a831c399e269772661';
      final String result = MD5Utils.hexMD5(input);
      expect(result, equals(expected));
    });

    test('should calculate correct MD5 hash for "abc"', () {
      const String input = 'abc';
      const String expected = '900150983cd24fb0d6963f7d28e17f72';
      final String result = MD5Utils.hexMD5(input);
      expect(result, equals(expected));
    });

    test('should calculate correct MD5 hash for "message digest"', () {
      const String input = 'message digest';
      const String expected = 'f96b697d7cb7938d525a2f31aaf161d0';
      final String result = MD5Utils.hexMD5(input);
      expect(result, equals(expected));
    });

    test('should calculate correct MD5 hash for alphabet', () {
      const String input = 'abcdefghijklmnopqrstuvwxyz';
      const String expected = 'c3fcd3d76192e4007dfb496cca67e13b';
      final String result = MD5Utils.hexMD5(input);
      expect(result, equals(expected));
    });

    test('should calculate correct MD5 hash for alphanumeric', () {
      const String input = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      const String expected = 'd174ab98d277d9f5a5611c2c9f419d9f';
      final String result = MD5Utils.hexMD5(input);
      expect(result, equals(expected));
    });

    test('should calculate correct MD5 hash for long string', () {
      const String input = '1234567890' * 8; // 80 characters
      const String expected = '57edf4a22be3c955ac49da2e2107b67a';
      final String result = MD5Utils.hexMD5(input);
      expect(result, equals(expected));
    });

    test('calcMD5 should be alias for hexMD5', () {
      const String input = 'test';
      final String hexResult = MD5Utils.hexMD5(input);
      final String calcResult = MD5Utils.calcMD5(input);
      expect(calcResult, equals(hexResult));
    });

    test('should calculate base64 MD5 hash', () {
      const String input = 'abc';
      final String result = MD5Utils.b64MD5(input);
      expect(result.length, greaterThan(0));
      // Base64 result should be different from hex result
      final String hexResult = MD5Utils.hexMD5(input);
      expect(result, isNot(equals(hexResult)));
    });

    test('should handle UTF-8 strings correctly', () {
      const String input = 'Hello 世界';
      final String result = MD5Utils.hexMD5FromUtf8(input);
      expect(result.length, equals(32));
      expect(result, matches(RegExp(r'^[a-f0-9]{32}$')));
    });

    test('should handle special characters', () {
      const String input = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
      final String result = MD5Utils.hexMD5(input);
      expect(result.length, equals(32));
      expect(result, matches(RegExp(r'^[a-f0-9]{32}$')));
    });
  });
}
