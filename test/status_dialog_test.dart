import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kit/src/shared/components/dialogs/status_dialog.dart';

void main() {
  group('StatusDialog Tests', () {
    late Widget testApp;

    setUp(() {
      testApp = MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () {},
              child: const Text('Test Button'),
            ),
          ),
        ),
      );
    });

    testWidgets('should show loading dialog', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      // 获取context
      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示加载弹窗
      StatusDialog.showLoading(context: context, message: '加载中...');
      await tester.pump();

      // 验证弹窗是否显示
      expect(StatusDialog.isVisible, isTrue);
      expect(find.text('加载中...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show success dialog', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示成功弹窗
      StatusDialog.showSuccess(context: context, message: '操作成功');
      await tester.pump();

      // 验证弹窗是否显示
      expect(StatusDialog.isVisible, isTrue);
      expect(find.text('操作成功'), findsOneWidget);
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('should show error dialog', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示错误弹窗
      StatusDialog.showError(context: context, message: '操作失败');
      await tester.pump();

      // 验证弹窗是否显示
      expect(StatusDialog.isVisible, isTrue);
      expect(find.text('操作失败'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should show timeout dialog', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示超时弹窗
      StatusDialog.showTimeout(context: context, message: '请求超时');
      await tester.pump();

      // 验证弹窗是否显示
      expect(StatusDialog.isVisible, isTrue);
      expect(find.text('请求超时'), findsOneWidget);
      expect(find.byIcon(Icons.access_time), findsOneWidget);
    });

    testWidgets('should hide dialog when hide is called', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示加载弹窗
      StatusDialog.showLoading(context: context);
      await tester.pump();
      expect(StatusDialog.isVisible, isTrue);

      // 隐藏弹窗
      StatusDialog.hide(context: context);
      await tester.pump();
      expect(StatusDialog.isVisible, isFalse);
    });

    testWidgets('should auto hide success dialog after duration', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示成功弹窗，设置1秒自动隐藏
      StatusDialog.showSuccess(
        context: context,
        message: '操作成功',
        autoHideDuration: const Duration(seconds: 1),
      );
      await tester.pump();
      expect(StatusDialog.isVisible, isTrue);

      // 等待1秒后检查是否自动隐藏
      await tester.pump(const Duration(seconds: 1));
      expect(StatusDialog.isVisible, isFalse);
    });

    testWidgets('should trigger timeout callback', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));
      bool timeoutCalled = false;

      // 显示加载弹窗，设置1秒超时
      StatusDialog.showLoading(
        context: context,
        message: '加载中...',
        timeoutDuration: const Duration(seconds: 1),
        onTimeout: () {
          timeoutCalled = true;
        },
      );
      await tester.pump();

      // 等待1秒后检查超时回调是否被调用
      await tester.pump(const Duration(seconds: 1));
      expect(timeoutCalled, isTrue);
    });

    testWidgets('should replace existing dialog when showing new one', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示加载弹窗
      StatusDialog.showLoading(context: context, message: '加载中...');
      await tester.pump();
      expect(find.text('加载中...'), findsOneWidget);

      // 显示成功弹窗，应该替换加载弹窗
      StatusDialog.showSuccess(context: context, message: '操作成功');
      await tester.pump(const Duration(milliseconds: 200)); // 等待弹窗切换

      expect(find.text('操作成功'), findsOneWidget);
      expect(find.text('加载中...'), findsNothing);
    });

    testWidgets('should trigger auto hide callback', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));
      bool autoHideCalled = false;

      // 显示成功弹窗，设置自动隐藏回调
      StatusDialog.showSuccess(
        context: context,
        message: '操作成功',
        autoHideDuration: const Duration(seconds: 1),
        onAutoHide: () {
          autoHideCalled = true;
        },
      );
      await tester.pump();

      // 等待1秒后检查自动隐藏回调是否被调用
      await tester.pump(const Duration(seconds: 1));
      expect(autoHideCalled, isTrue);
    });

    testWidgets('should not be dismissible by tapping outside', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 显示加载弹窗
      StatusDialog.showLoading(context: context);
      await tester.pump();
      expect(StatusDialog.isVisible, isTrue);

      // 尝试点击弹窗外部区域
      await tester.tapAt(const Offset(10, 10));
      await tester.pump();

      // 弹窗应该仍然显示（不可通过点击外部关闭）
      expect(StatusDialog.isVisible, isTrue);
    });

    testWidgets('should use default messages when not provided', (WidgetTester tester) async {
      await tester.pumpWidget(testApp);

      final BuildContext context = tester.element(find.byType(ElevatedButton));

      // 测试默认加载消息
      StatusDialog.showLoading(context: context);
      await tester.pump();
      expect(find.text('加载中...'), findsOneWidget);

      StatusDialog.hide(context: context);
      await tester.pump();

      // 测试默认成功消息
      StatusDialog.showSuccess(context: context);
      await tester.pump();
      expect(find.text('操作成功'), findsOneWidget);

      StatusDialog.hide(context: context);
      await tester.pump();

      // 测试默认错误消息
      StatusDialog.showError(context: context);
      await tester.pump();
      expect(find.text('操作失败'), findsOneWidget);

      StatusDialog.hide(context: context);
      await tester.pump();

      // 测试默认超时消息
      StatusDialog.showTimeout(context: context);
      await tester.pump();
      expect(find.text('请求超时'), findsOneWidget);
    });
  });
}
