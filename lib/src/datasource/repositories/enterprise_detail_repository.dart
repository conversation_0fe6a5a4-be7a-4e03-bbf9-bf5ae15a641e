import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/enterprise_detail_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';

class EnterpriseDetailRepository extends BaseRepository {
  final ApiService apiService;

  EnterpriseDetailRepository({ApiService? apiService})
      : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  Future<UnifiedResponse<EnterpriseDetailEntity>> getContentData(
      String entId
      ) async{
    return runApiCall(call: () async{
      return await apiService.getEnterprise(entId);
    });
  }
}
