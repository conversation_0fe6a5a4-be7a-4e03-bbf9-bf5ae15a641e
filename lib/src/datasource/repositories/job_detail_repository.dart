import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/models/api_response/api_response.dart';
import 'package:flutter_kit/src/base/core/base_result.dart';
import 'package:flutter_kit/src/datasource/models/job_detail_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';

import '../models/api_response/unified_response.dart';

class JobDetailRepository extends BaseRepository {
  final ApiService apiService;

  JobDetailRepository({
    ApiService? apiService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  Future<UnifiedResponse<JobDetailEntity>> getContentData(
      String jobId) async {
    return runApiCall(call: () async{
      return await apiService.getJob(jobId);
    });
  }
}
