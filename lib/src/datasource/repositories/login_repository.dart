import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/shared/locator.dart';

/// 登录相关数据仓库
class LoginRepository extends BaseRepository {
  final ApiService apiService;

  LoginRepository({
    ApiService? apiService,
  }) : apiService = apiService ?? ApiService(dio: locator<DioConfig>().dio);

  /// 手机号/密码/验证码 登录
  Future<UnifiedResponse<String>> ExamineeLogin(
      {required String phone,
      required String password,
      required String ident,
      required String vercode}) async {
    return runApiCall(call: () async {
      return await apiService.ExamineeLogin(phone, password, ident, vercode);
    });
  }

  //图形验证码
  Future<UnifiedResponse<String>> getImage(
      {required String width,
      required String height,
      required int type}) async {
    return runApiCall(call: () async {
      return await apiService.getImage(width, height, type);
    });
  }
}
