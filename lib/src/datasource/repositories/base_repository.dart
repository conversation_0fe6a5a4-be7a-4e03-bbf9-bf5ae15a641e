import 'package:dio/dio.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';

typedef ApiCall<T> = Future<UnifiedResponse<T>> Function();

abstract class BaseRepository {
  Future<UnifiedResponse<T>> runApiCall<T>({required ApiCall<T> call}) async {
    try {
      final response = await call();
      return response;
    } on DioException catch (e) {
      return UnifiedResponse.failure(
        _getDioErrorMessage(e),
        code: e.response?.statusCode ?? -1,
        error: e,
      );
    } catch (e) {
      return UnifiedResponse.failure(
        '未知错误: $e',
        code: -1,
        error: e,
      );
    }
  }

  /// 处理API响应的通用方法
  UnifiedResponse<T> handleResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    try {
      if (response.statusCode == 200) {
        final data = response.data;
        if (data is Map<String, dynamic>) {
          // 检查是否是统一响应格式
          if (data.containsKey('code') && data.containsKey('data')) {
            final code = data['code'];
            final message = data['message'] ?? '';

            if (code == 200 || code == 0) {
              final responseData = data['data'];
              if (responseData != null) {
                return UnifiedResponse.success(fromJson(responseData), message: message);
              } else {
                return UnifiedResponse.empty(message: message);
              }
            } else {
              return UnifiedResponse.failure(message, code: code);
            }
          } else {
            // 直接解析数据
            return UnifiedResponse.success(fromJson(data));
          }
        } else {
          return UnifiedResponse.failure('响应数据格式错误');
        }
      } else {
        return UnifiedResponse.failure('请求失败: ${response.statusCode}');
      }
    } catch (e) {
      return UnifiedResponse.failure('数据解析失败: $e');
    }
  }

  String _getDioErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时';
      case DioExceptionType.sendTimeout:
        return '发送超时';
      case DioExceptionType.receiveTimeout:
        return '接收超时';
      case DioExceptionType.badResponse:
        return '服务器响应错误: ${error.response?.statusCode}';
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接错误';
      case DioExceptionType.unknown:
      default:
        return '网络请求失败';
    }
  }
}
