import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/job_detail_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/job_detail_entity.g.dart';

@JsonSerializable()
class JobDetailEntity {
	@JSONField(name: 'Id')
	late String id;
	@JSONField(name: 'Name')
	late String name;
	@JSONField(name: 'Pay')
	late String pay;
	@JSONField(name: 'JobPayUnit')
	late String jobPayUnit;
	@JSONField(name: 'UserId')
	late String userId;
	@JSONField(name: 'EnterpriseId')
	late String enterpriseId;
	@JSONField(name: 'EnterpriseName')
	late String enterpriseName;
	@JSONField(name: 'UserAuths')
	late List<JobDetailUserAuths> userAuths;
	@JSONField(name: 'EnterpriseNatureCode')
	late String enterpriseNatureCode;
	@JSONField(name: 'EnterpriseRegisteredCapitalCode')
	late String enterpriseRegisteredCapitalCode;
	@JSONField(name: 'EnterprisePeopleNumCode')
	late String enterprisePeopleNumCode;
	@JSONField(name: 'EnterpriseLogoSmall')
	late String enterpriseLogoSmall;
	@JSONField(name: 'ShareLogo')
	late String shareLogo;
	@JSONField(name: 'Welfare')
	late String welfare;
	@JSONField(name: 'WelfareValue')
	late String welfareValue;
	@JSONField(name: 'ReleaseTime')
	late String releaseTime;
	@JSONField(name: 'MapLng')
	late String mapLng;
	@JSONField(name: 'MapLat')
	late String mapLat;
	@JSONField(name: 'Distance')
	late int distance;
	@JSONField(name: 'RecruitingCount')
	late int recruitingCount;
	@JSONField(name: 'JobTypeId')
	late String jobTypeId;
	@JSONField(name: 'JobTypeName')
	late String jobTypeName;
	@JSONField(name: 'Nature')
	late String nature;
	@JSONField(name: 'Department')
	late String department;
	@JSONField(name: 'PayWay')
	late String payWay;
	@JSONField(name: 'MapLocation')
	late String mapLocation;
	@JSONField(name: 'DemandEducationCode')
	late String demandEducationCode;
	@JSONField(name: 'Street')
	late String street;
	@JSONField(name: 'Describe')
	late String describe;
	@JSONField(name: 'ApplyCount')
	late int applyCount;
	@JSONField(name: 'Accommodation')
	late String accommodation;
	@JSONField(name: 'WorkAddress')
	late String workAddress;
	@JSONField(name: 'IsHot')
	late bool isHot;
	@JSONField(name: 'IsSelf')
	late bool isSelf;
	@JSONField(name: 'ContactManName')
	late String contactManName;
	@JSONField(name: 'ContactManPhone')
	late String contactManPhone;
	@JSONField(name: 'IsPutaway')
	late bool isPutaway;
	@JSONField(name: 'ResumeCount')
	late int resumeCount;
	@JSONField(name: 'ReleaseChannel')
	late String releaseChannel;

	JobDetailEntity();

	factory JobDetailEntity.fromJson(Map<String, dynamic> json) => $JobDetailEntityFromJson(json);

	Map<String, dynamic> toJson() => $JobDetailEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class JobDetailUserAuths {
	@JSONField(name: 'Type')
	late String type;
	@JSONField(name: 'AddTime')
	late String addTime;

	JobDetailUserAuths();

	factory JobDetailUserAuths.fromJson(Map<String, dynamic> json) => $JobDetailUserAuthsFromJson(json);

	Map<String, dynamic> toJson() => $JobDetailUserAuthsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}