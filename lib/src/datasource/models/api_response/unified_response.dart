import 'package:equatable/equatable.dart';
import '../../../../generated/json/base/json_convert_content.dart';

/// 统一的API响应处理类
/// 合并了 ApiResponse 和 BaseResult 的功能
sealed class UnifiedResponse<T> extends Equatable {
  const UnifiedResponse();

  /// 成功响应
  factory UnifiedResponse.success(T data, {String? message}) = UnifiedResponseSuccess;
  
  /// 失败响应
  factory UnifiedResponse.failure(String message, {int? code, dynamic error}) = UnifiedResponseFailure;
  
  /// 空数据响应
  factory UnifiedResponse.empty({String? message}) = UnifiedResponseEmpty;

  /// 从JSON创建响应对象
  factory UnifiedResponse.fromJson(Map<String, dynamic> json) {
    try {
      final code = json['code'];
      final msg = json['msg'] ?? json['message'] ?? '';
      
      // 判断是否成功
      bool isSuccess = false;
      if (code is int) {
        isSuccess = code == 200;
      } else if (code is String) {
        isSuccess = code == '0';
      }

      if (isSuccess) {
        // 处理数据字段 - 兼容 'data' 和 'body' 字段
        dynamic dataField = json['data'] ?? json['body'];
        
        if (dataField == null || dataField == 'null') {
          return UnifiedResponse.empty(message: msg);
        }

        T? data;
        try {
          if (T == dynamic) {
            data = dataField as T;
          } else {
            data = JsonConvert.fromJsonAsT<T>(dataField);
          }
        } catch (e) {
          // 处理特殊情况，如 List<String>
          if (dataField is List && T.toString().contains('List')) {
            data = List<String>.from(dataField) as T;
          } else {
            data = dataField as T;
          }
        }

        if (data != null) {
          // 检查是否为空数据
          if (data is List && (data as List).isEmpty) {
            return UnifiedResponse.empty(message: msg);
          }
          return UnifiedResponse.success(data, message: msg);
        } else {
          return UnifiedResponse.empty(message: msg);
        }
      } else {
        return UnifiedResponse.failure(
          msg, 
          code: code is int ? code : (code is String ? int.tryParse(code) : -1)
        );
      }
    } catch (e) {
      return UnifiedResponse.failure('解析响应数据失败: $e', code: -1, error: e);
    }
  }

  /// 模式匹配处理
  R when<R>({
    required R Function(T data, String? message) success,
    required R Function(String message, int? code, dynamic error) failure,
    required R Function(String? message) empty,
  }) {
    return switch (this) {
      UnifiedResponseSuccess<T> s => success(s.data, s.message),
      UnifiedResponseFailure<T> f => failure(f.message, f.code, f.error),
      UnifiedResponseEmpty<T> e => empty(e.message),
    };
  }

  /// 便捷方法
  bool get isSuccess => this is UnifiedResponseSuccess<T>;
  bool get isFailure => this is UnifiedResponseFailure<T>;
  bool get isEmpty => this is UnifiedResponseEmpty<T>;

  /// 获取数据（仅在成功时有效）
  T? get data => switch (this) {
    UnifiedResponseSuccess<T> s => s.data,
    _ => null,
  };

  /// 获取错误信息
  String? get errorMessage => switch (this) {
    UnifiedResponseFailure<T> f => f.message,
    _ => null,
  };

  @override
  List<Object?> get props => [];
}

/// 成功响应
final class UnifiedResponseSuccess<T> extends UnifiedResponse<T> {
  final T data;
  final String? message;

  const UnifiedResponseSuccess(this.data, {this.message});

  @override
  List<Object?> get props => [data, message];
}

/// 失败响应
final class UnifiedResponseFailure<T> extends UnifiedResponse<T> {
  final String message;
  final int? code;
  final dynamic error;

  const UnifiedResponseFailure(this.message, {this.code, this.error});

  @override
  List<Object?> get props => [message, code, error];
}

/// 空数据响应
final class UnifiedResponseEmpty<T> extends UnifiedResponse<T> {
  final String? message;

  const UnifiedResponseEmpty({this.message});

  @override
  List<Object?> get props => [message];
}

/// 扩展方法，用于分页判断
extension UnifiedResponsePaging<T> on UnifiedResponse<T> {
  /// 判断是否还有更多数据（用于分页）
  bool hasMoreData(int pageSize) {
    if (data is List) {
      return (data as List).length >= pageSize;
    }
    return false;
  }

  /// 判断是否没有更多数据
  bool get noMoreData => !hasMoreData(10); // 默认页面大小为10
}
