import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/resume_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/resume_entity.g.dart';

@JsonSerializable()
class ResumeEntity {
	@J<PERSON><PERSON>ield(name: "Id")
	late String id;
	@JSONField(name: "Title")
	late String title;
	@JSONField(name: "Realname")
	late String realname;
	@JSONField(name: "UserAuth<PERSON>")
	late List<dynamic> userAuths;
	@JSONField(name: "GenderCode")
	late String genderCode;
	@JSONField(name: "NationCode")
	late String nationCode;
	@JSONField(name: "MaritalStatusCode")
	late String maritalStatusCode;
	@J<PERSON>NField(name: "Stature")
	late int stature;
	@JSONField(name: "Address")
	late String address;
	@JSONField(name: "ComputerLevelCode")
	late String computerLevelCode;
	@JSONField(name: "EnglishLevelCode")
	late String englishLevelCode;
	@JSONField(name: "NativePlaceAreaId")
	late String nativePlaceAreaId;
	@JSONField(name: "NativePlaceAreaName")
	late String nativePlaceAreaName;
	@JSONField(name: "NativePlaceAreaCascadeName")
	late String nativePlaceAreaCascadeName;
	@JSONField(name: "GraduateSchool")
	late String graduateSchool;
	@JSONField(name: "MajorIn")
	late String majorIn;
	@JSONField(name: "HeadImage")
	late String headImage;
	@JSONField(name: "Idcard")
	late String idcard;
	@JSONField(name: "Gender")
	late String gender;
	@JSONField(name: "Nation")
	late String nation;
	@JSONField(name: "QQ")
	late String qQ;
	@JSONField(name: "Birthday")
	late String birthday;
	@JSONField(name: "LiveAreaId")
	late String liveAreaId;
	@JSONField(name: "LiveAreaName")
	late String liveAreaName;
	@JSONField(name: "LiveAreaCascadeName")
	late String liveAreaCascadeName;
	@JSONField(name: "IntentionAreaIds")
	late String intentionAreaIds;
	@JSONField(name: "IntentionAreaNames")
	late String intentionAreaNames;
	@JSONField(name: "IntentionJobTypeId")
	late String intentionJobTypeId;
	@JSONField(name: "IntentionJobType")
	late String intentionJobType;
	@JSONField(name: "IntentionJobTypeIds")
	late String intentionJobTypeIds;
	@JSONField(name: "IntentionJobTypeNames")
	late String intentionJobTypeNames;
	@JSONField(name: "WorkStatusCode")
	late String workStatusCode;
	@JSONField(name: "WorkingAgeCode")
	late String workingAgeCode;
	@JSONField(name: "IntentionPayCode")
	late String intentionPayCode;
	@JSONField(name: "IntentionPayValue")
	late String intentionPayValue;
	@JSONField(name: "IntentionPayWayCode")
	late String intentionPayWayCode;
	@JSONField(name: "EducationCode")
	late String educationCode;
	@JSONField(name: "WorkExperience")
	late List<ResumeWorkExperience> workExperience;
	@JSONField(name: "EduExperience")
	late List<ResumeEduExperience> eduExperience;
	@JSONField(name: "PersonalProfile")
	late String personalProfile;
	@JSONField(name: "Proportion")
	late double proportion;
	@JSONField(name: "Certificate")
	late String certificate;
	@JSONField(name: "Skill")
	late String skill;
	@JSONField(name: "IsDefault")
	late bool isDefault;
	@JSONField(name: "Integral")
	late int integral;
	@JSONField(name: "Phone")
	late String phone;
	@JSONField(name: "Email")
	late String email;
	@JSONField(name: "UserId")
	late String userId;
	@JSONField(name: "ReleaseTime")
	late String releaseTime;
	@JSONField(name: "Labels")
	late String labels;
	@JSONField(name: "MapLocation")
	late String mapLocation;
	@JSONField(name: "Street")
	late String street;
	@JSONField(name: "JobSeekerGroupCode")
	late String jobSeekerGroupCode;
	@JSONField(name: "SelectCount")
	late int selectCount;

	ResumeEntity();

	factory ResumeEntity.fromJson(Map<String, dynamic> json) => $ResumeEntityFromJson(json);

	Map<String, dynamic> toJson() => $ResumeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ResumeWorkExperience {
	@JSONField(name: "JobCompany")
	late String jobCompany;
	@JSONField(name: "JobName")
	late String jobName;
	@JSONField(name: "JobinTime")
	late String jobinTime;
	@JSONField(name: "JoboutTime")
	late String joboutTime;
	@JSONField(name: "JobDetail")
	late String jobDetail;

	ResumeWorkExperience();

	factory ResumeWorkExperience.fromJson(Map<String, dynamic> json) => $ResumeWorkExperienceFromJson(json);

	Map<String, dynamic> toJson() => $ResumeWorkExperienceToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ResumeEduExperience {
	@JSONField(name: "EduSchool")
	late String eduSchool;
	@JSONField(name: "EduProfessional")
	late String eduProfessional;
	@JSONField(name: "EduLevelValue")
	late String eduLevelValue;
	@JSONField(name: "EduinTime")
	late String eduinTime;
	@JSONField(name: "EduoutTime")
	late String eduoutTime;

	ResumeEduExperience();

	factory ResumeEduExperience.fromJson(Map<String, dynamic> json) => $ResumeEduExperienceFromJson(json);

	Map<String, dynamic> toJson() => $ResumeEduExperienceToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}