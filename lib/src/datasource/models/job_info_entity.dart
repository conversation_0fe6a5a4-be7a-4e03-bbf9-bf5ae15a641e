import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/job_info_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/job_info_entity.g.dart';

@JsonSerializable()
class JobInfoEntity {
	@JSONField(name: 'Id')
	late String id;
	@JSONField(name: 'EnterpriseId')
	late String enterpriseId;
	@JSONField(name: 'EnterpriseName')
	late String enterpriseName;
	@JSONField(name: 'UserAuths')
	late List<JobInfoUserAuths> userAuths;
	@JSONField(name: 'EnterpriseLogoSmall')
	late String enterpriseLogoSmall;
	@JSONField(name: 'Name')
	late String name;
	@JSONField(name: 'Nature')
	late String nature;
	@JSONField(name: 'JobTypeId')
	late String jobTypeId;
	@JSONField(name: 'JobTypeName')
	late String jobTypeName;
	@JSONField(name: 'Department')
	late String department;
	@JSONField(name: 'RecruitingCount')
	late int recruitingCount;
	@JSONField(name: 'PayWay')
	late String payWay;
	@JSONField(name: 'WorkAreaId')
	late String workAreaId;
	@JSONField(name: 'WorkAreaName')
	late String workAreaName;
	@JSONField(name: 'WorkAreaCascadeName')
	late String workAreaCascadeName;
	@JSONField(name: 'Pay')
	late String pay;
	@JSONField(name: 'JobPayUnit')
	late String jobPayUnit;
	@JSONField(name: 'Welfare')
	late String welfare;
	@JSONField(name: 'WelfareValue')
	late String welfareValue;
	@JSONField(name: 'MapLocation')
	late String mapLocation;
	@JSONField(name: 'Street')
	late String street;
	@JSONField(name: 'DemandEducationCode')
	late String demandEducationCode;
	@JSONField(name: 'WorkAddress')
	late String workAddress;
	@JSONField(name: 'ReleaseTime')
	late String releaseTime;
	@JSONField(name: 'IsPutaway')
	late bool isPutaway;
	@JSONField(name: 'ApplyCount')
	late int applyCount;
	@JSONField(name: 'SelectCount')
	late int selectCount;
	@JSONField(name: 'ReleaseChannel')
	late String releaseChannel;

	JobInfoEntity();

	factory JobInfoEntity.fromJson(Map<String, dynamic> json) => $JobInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $JobInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class JobInfoUserAuths {
	@JSONField(name: 'Type')
	late String type;
	@JSONField(name: 'AddTime')
	late String addTime;

	JobInfoUserAuths();

	factory JobInfoUserAuths.fromJson(Map<String, dynamic> json) => $JobInfoUserAuthsFromJson(json);

	Map<String, dynamic> toJson() => $JobInfoUserAuthsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}