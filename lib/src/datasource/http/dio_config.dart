import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter_kit/src/core/environment.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';

final _logInterceptor = LogInterceptor(
  logPrint: (object) => log(object.toString()),
  request: true,
  requestHeader: true,
  requestBody: true,
  responseBody: true,
);

class DioConfig {
  final Dio dio;

  DioConfig({Dio? dio})
      : dio = dio ??
            Dio(
              BaseOptions(
                baseUrl: Environment.baseUrl,
                headers: {
                  'Accept': 'application/json',
                },
                contentType: 'application/json',
              ),
            ) {
    this.dio.interceptors.add(_logInterceptor);
    this.dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final needAuth = options.extra['auth'] != false;
          if (needAuth) {
            final storage = locator<Storage>();
            final token = await storage.read<String>(key: 'examinee_token');
            if (token != null && token.isNotEmpty) {
              options.headers['Authorization'] = 'Bearer $token';
            }
          }
          return handler.next(options);
        },
      ),
    );
  }
}
