import 'package:dio/dio.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/enterprise_detail_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_detail_entity.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';

class ApiService {
  final Dio dio;

  ApiService({required this.dio});

  Future<String> getExample() async {
    await Future.delayed(const Duration(seconds: 2));
    return 'Example';
  }

  Future<String> getExamplePublic() async {
    await dio.get('/public', options: Options(extra: {'auth': false}));
    return 'Example Public';
  }

  //求职端 - 首页
  Future<UnifiedResponse<List<JobInfoEntity>>> getHomeList() async {
    final response = await dio.get('/Common/Job/GetHomeJobList');
    return UnifiedResponse<List<JobInfoEntity>>.fromJson(response.data);
  }

  ///求职端 - 获取职位详情
  Future<UnifiedResponse<JobDetailEntity>> getJob(String jobId) async {
    final response = await dio.get(
      '/Common/Job/GetJob',
      queryParameters: {'jobId': jobId},
    );
    return UnifiedResponse<JobDetailEntity>.fromJson(response.data);
  }

  ///求职端 - 企业详情
  Future<UnifiedResponse<EnterpriseDetailEntity>> getEnterprise(
      String entId) async {
    final response = await dio.get(
      '/Common/Enterprise/GetEnterprise',
      queryParameters: {'entId': entId},
    );
    return UnifiedResponse<EnterpriseDetailEntity>.fromJson(response.data);
  }

  ///求职端 - 登录
  Future<UnifiedResponse<String>> ExamineeLogin(
      String userName, String password, String ident, String vercode) async {
    final response = await dio.post('/JobSeeker/User/Login', data: {
      'UserName': userName,
      'Password': password,
      'Ident': ident,
      'Vercode': vercode
    });
    return UnifiedResponse<String>.fromJson(response.data);
  }

  ///公共 - 获取图形验证码
  Future<UnifiedResponse<String>> getImage(
      String width, String height, int type) async {
    final response = await dio.get('/Common/Captcha/GetImage',
        queryParameters: {'Width': width, 'Height': height, 'Type': type});
    return UnifiedResponse<String>.fromJson(response.data);
  }
}
