import 'package:flutter_kit/src/core/routing/app_router.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/repositories/enterprise_detail_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/home_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/job_detail_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/login_repository.dart';
import 'package:flutter_kit/src/features/discover/logic/discover_logic.dart';
import 'package:flutter_kit/src/features/home/<USER>/home_logic.dart';
import 'package:flutter_kit/src/features/main_tab/logic/main_tab_logic.dart';
import 'package:flutter_kit/src/features/messages/logic/messages_logic.dart';
import 'package:flutter_kit/src/features/profile/logic/profile_logic.dart';
import 'package:flutter_kit/src/features/resume/logic/resume_logic.dart';
import 'package:flutter_kit/src/features/splash/logic/splash_logic.dart';

import 'package:flutter_kit/src/shared/services/app_logger.dart';
import 'package:flutter_kit/src/shared/services/storage/local_storage.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';
import 'package:get_it/get_it.dart';

final GetIt locator = GetIt.instance
  ..registerLazySingleton(() => DioConfig())
  ..registerLazySingleton(() => AppRouter())
  ..registerLazySingleton<AppLogger>(() => AppLogger())
  ..registerLazySingleton<Storage>(() => LocalStorage())
  ..registerLazySingleton(() => ApiService(dio: locator<DioConfig>().dio))
  ..registerLazySingleton(() => HomeReportsitory())
  ..registerLazySingleton(() => JobDetailRepository())
  ..registerLazySingleton(()=>EnterpriseDetailRepository())
  ..registerFactory(() => HomeLogic(repository: locator<HomeReportsitory>()))
  ..registerFactory(() => MainTabLogic())
  ..registerFactory(() => DiscoverLogic())
  ..registerFactory(() => MessagesLogic())
  ..registerFactory(()=>ResumeLogic())
  ..registerFactory(() => ProfileLogic())
  ..registerFactory(()=>LoginRepository())
  ..registerFactory(() => SplashLogic());
