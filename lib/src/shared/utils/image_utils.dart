import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';

/// 图片工具类
class ImageUtils {
  /// 将base64字符串转换为Image Widget
  /// 
  /// [base64String] base64编码的图片字符串
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [fit] 图片适配方式
  /// [errorWidget] 错误时显示的Widget
  static Widget base64ToImage(
    String base64String, {
    double? width,
    double? height,
    BoxFit? fit,
    Widget? errorWidget,
  }) {
    try {
      // 移除base64前缀（如果存在）
      String cleanBase64 = _cleanBase64String(base64String);
      
      // 解码base64字符串
      Uint8List bytes = base64Decode(cleanBase64);
      
      return Image.memory(
        bytes,
        width: width,
        height: height,
        fit: fit ?? BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ?? _buildErrorWidget(width, height);
        },
      );
    } catch (e) {
      print('Base64图片解析失败: $e');
      return errorWidget ?? _buildErrorWidget(width, height);
    }
  }

  /// 将base64字符串转换为Uint8List
  /// 
  /// [base64String] base64编码的图片字符串
  /// 返回图片的字节数据，如果解析失败返回null
  static Uint8List? base64ToBytes(String base64String) {
    try {
      String cleanBase64 = _cleanBase64String(base64String);
      return base64Decode(cleanBase64);
    } catch (e) {
      print('Base64字节转换失败: $e');
      return null;
    }
  }

  /// 检查base64字符串是否有效
  /// 
  /// [base64String] 要检查的base64字符串
  /// 返回true表示有效，false表示无效
  static bool isValidBase64Image(String base64String) {
    try {
      String cleanBase64 = _cleanBase64String(base64String);
      if (cleanBase64.isEmpty) return false;
      
      // 尝试解码
      base64Decode(cleanBase64);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 清理base64字符串，移除前缀和空白字符
  /// 
  /// [base64String] 原始base64字符串
  /// 返回清理后的base64字符串
  static String _cleanBase64String(String base64String) {
    if (base64String.isEmpty) return '';
    
    String cleaned = base64String.trim();
    
    // 移除常见的base64前缀
    const prefixes = [
      'data:image/png;base64,',
      'data:image/jpeg;base64,',
      'data:image/jpg;base64,',
      'data:image/gif;base64,',
      'data:image/webp;base64,',
      'data:image/bmp;base64,',
      'data:image/svg+xml;base64,',
    ];
    
    for (String prefix in prefixes) {
      if (cleaned.startsWith(prefix)) {
        cleaned = cleaned.substring(prefix.length);
        break;
      }
    }
    
    // 移除换行符和空格
    cleaned = cleaned.replaceAll(RegExp(r'\s'), '');
    
    return cleaned;
  }

  /// 构建错误显示Widget
  /// 
  /// [width] 宽度
  /// [height] 高度
  /// 返回错误显示的Widget
  static Widget _buildErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              color: Colors.grey,
              size: 24,
            ),
            SizedBox(height: 4),
            Text(
              '图片加载失败',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取图片格式
  /// 
  /// [base64String] base64编码的图片字符串
  /// 返回图片格式（png, jpeg, gif等），如果无法识别返回null
  static String? getImageFormat(String base64String) {
    if (base64String.isEmpty) return null;
    
    // 从data URL中提取格式
    if (base64String.startsWith('data:image/')) {
      final match = RegExp(r'data:image/(\w+);base64,').firstMatch(base64String);
      if (match != null) {
        return match.group(1);
      }
    }
    
    // 通过文件头判断格式
    try {
      String cleanBase64 = _cleanBase64String(base64String);
      Uint8List bytes = base64Decode(cleanBase64);
      
      if (bytes.length < 4) return null;
      
      // PNG: 89 50 4E 47
      if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
        return 'png';
      }
      
      // JPEG: FF D8 FF
      if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
        return 'jpeg';
      }
      
      // GIF: 47 49 46
      if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46) {
        return 'gif';
      }
      
      // WebP: 52 49 46 46 (RIFF)
      if (bytes.length >= 12 && 
          bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
          bytes[8] == 0x57 && bytes[9] == 0x45 && bytes[10] == 0x42 && bytes[11] == 0x50) {
        return 'webp';
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 创建带有加载状态的base64图片Widget
  /// 
  /// [base64String] base64编码的图片字符串
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [fit] 图片适配方式
  /// [loadingWidget] 加载时显示的Widget
  /// [errorWidget] 错误时显示的Widget
  static Widget base64ImageWithLoading(
    String base64String, {
    double? width,
    double? height,
    BoxFit? fit,
    Widget? loadingWidget,
    Widget? errorWidget,
  }) {
    if (base64String.isEmpty) {
      return loadingWidget ?? _buildLoadingWidget(width, height);
    }
    
    return base64ToImage(
      base64String,
      width: width,
      height: height,
      fit: fit,
      errorWidget: errorWidget,
    );
  }

  /// 构建加载状态Widget
  /// 
  /// [width] 宽度
  /// [height] 高度
  /// 返回加载状态的Widget
  static Widget _buildLoadingWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
          ),
        ),
      ),
    );
  }
}
