import 'dart:convert';
import 'dart:typed_data';

/// A Dart implementation of the RSA Data Security, Inc. MD5 Message
/// Digest Algorithm, as defined in RFC 1321.
/// 
/// This implementation is based on the JavaScript version by <PERSON>
/// and provides the same functionality for Flutter/Dart applications.
class MD5Utils {
  /// Add integers, wrapping at 2^32. This uses 32-bit operations internally.
  static int _safeAdd(int x, int y) {
    final int lsw = (x & 0xFFFF) + (y & 0xFFFF);
    final int msw = (x >> 16) + (y >> 16) + (lsw >> 16);
    return ((msw << 16) | (lsw & 0xFFFF)) & 0xFFFFFFFF;
  }

  /// Bitwise rotate a 32-bit number to the left.
  static int _rol(int num, int cnt) {
    return (((num << cnt) | (num >> (32 - cnt))) & 0xFFFFFFFF);
  }

  /// Common function used by the four basic operations.
  static int _cmn(int q, int a, int b, int x, int s, int t) {
    return _safeAdd(_rol(_safeAdd(_safeAdd(a, q), _safeAdd(x, t)), s), b);
  }

  /// MD5 basic operation F.
  static int _ff(int a, int b, int c, int d, int x, int s, int t) {
    return _cmn((b & c) | ((~b) & d), a, b, x, s, t);
  }

  /// MD5 basic operation G.
  static int _gg(int a, int b, int c, int d, int x, int s, int t) {
    return _cmn((b & d) | (c & (~d)), a, b, x, s, t);
  }

  /// MD5 basic operation H.
  static int _hh(int a, int b, int c, int d, int x, int s, int t) {
    return _cmn(b ^ c ^ d, a, b, x, s, t);
  }

  /// MD5 basic operation I.
  static int _ii(int a, int b, int c, int d, int x, int s, int t) {
    return _cmn(c ^ (b | (~d)), a, b, x, s, t);
  }

  /// Calculate the MD5 of an array of little-endian words, producing an array
  /// of little-endian words.
  static List<int> _coreMD5(List<int> x) {
    int a = 0x67452301;
    int b = 0xEFCDAB89;
    int c = 0x98BADCFE;
    int d = 0x10325476;

    for (int i = 0; i < x.length; i += 16) {
      final int olda = a;
      final int oldb = b;
      final int oldc = c;
      final int oldd = d;

      a = _ff(a, b, c, d, x[i + 0], 7, 0xD76AA478);
      d = _ff(d, a, b, c, x[i + 1], 12, 0xE8C7B756);
      c = _ff(c, d, a, b, x[i + 2], 17, 0x242070DB);
      b = _ff(b, c, d, a, x[i + 3], 22, 0xC1BDCEEE);
      a = _ff(a, b, c, d, x[i + 4], 7, 0xF57C0FAF);
      d = _ff(d, a, b, c, x[i + 5], 12, 0x4787C62A);
      c = _ff(c, d, a, b, x[i + 6], 17, 0xA8304613);
      b = _ff(b, c, d, a, x[i + 7], 22, 0xFD469501);
      a = _ff(a, b, c, d, x[i + 8], 7, 0x698098D8);
      d = _ff(d, a, b, c, x[i + 9], 12, 0x8B44F7AF);
      c = _ff(c, d, a, b, x[i + 10], 17, 0xFFFF5BB1);
      b = _ff(b, c, d, a, x[i + 11], 22, 0x895CD7BE);
      a = _ff(a, b, c, d, x[i + 12], 7, 0x6B901122);
      d = _ff(d, a, b, c, x[i + 13], 12, 0xFD987193);
      c = _ff(c, d, a, b, x[i + 14], 17, 0xA679438E);
      b = _ff(b, c, d, a, x[i + 15], 22, 0x49B40821);

      a = _gg(a, b, c, d, x[i + 1], 5, 0xF61E2562);
      d = _gg(d, a, b, c, x[i + 6], 9, 0xC040B340);
      c = _gg(c, d, a, b, x[i + 11], 14, 0x265E5A51);
      b = _gg(b, c, d, a, x[i + 0], 20, 0xE9B6C7AA);
      a = _gg(a, b, c, d, x[i + 5], 5, 0xD62F105D);
      d = _gg(d, a, b, c, x[i + 10], 9, 0x02441453);
      c = _gg(c, d, a, b, x[i + 15], 14, 0xD8A1E681);
      b = _gg(b, c, d, a, x[i + 4], 20, 0xE7D3FBC8);
      a = _gg(a, b, c, d, x[i + 9], 5, 0x21E1CDE6);
      d = _gg(d, a, b, c, x[i + 14], 9, 0xC33707D6);
      c = _gg(c, d, a, b, x[i + 3], 14, 0xF4D50D87);
      b = _gg(b, c, d, a, x[i + 8], 20, 0x455A14ED);
      a = _gg(a, b, c, d, x[i + 13], 5, 0xA9E3E905);
      d = _gg(d, a, b, c, x[i + 2], 9, 0xFCEFA3F8);
      c = _gg(c, d, a, b, x[i + 7], 14, 0x676F02D9);
      b = _gg(b, c, d, a, x[i + 12], 20, 0x8D2A4C8A);

      a = _hh(a, b, c, d, x[i + 5], 4, 0xFFFA3942);
      d = _hh(d, a, b, c, x[i + 8], 11, 0x8771F681);
      c = _hh(c, d, a, b, x[i + 11], 16, 0x6D9D6122);
      b = _hh(b, c, d, a, x[i + 14], 23, 0xFDE5380C);
      a = _hh(a, b, c, d, x[i + 1], 4, 0xA4BEEA44);
      d = _hh(d, a, b, c, x[i + 4], 11, 0x4BDECFA9);
      c = _hh(c, d, a, b, x[i + 7], 16, 0xF6BB4B60);
      b = _hh(b, c, d, a, x[i + 10], 23, 0xBEBFBC70);
      a = _hh(a, b, c, d, x[i + 13], 4, 0x289B7EC6);
      d = _hh(d, a, b, c, x[i + 0], 11, 0xEAA127FA);
      c = _hh(c, d, a, b, x[i + 3], 16, 0xD4EF3085);
      b = _hh(b, c, d, a, x[i + 6], 23, 0x04881D05);
      a = _hh(a, b, c, d, x[i + 9], 4, 0xD9D4D039);
      d = _hh(d, a, b, c, x[i + 12], 11, 0xE6DB99E5);
      c = _hh(c, d, a, b, x[i + 15], 16, 0x1FA27CF8);
      b = _hh(b, c, d, a, x[i + 2], 23, 0xC4AC5665);

      a = _ii(a, b, c, d, x[i + 0], 6, 0xF4292244);
      d = _ii(d, a, b, c, x[i + 7], 10, 0x432AFF97);
      c = _ii(c, d, a, b, x[i + 14], 15, 0xAB9423A7);
      b = _ii(b, c, d, a, x[i + 5], 21, 0xFC93A039);
      a = _ii(a, b, c, d, x[i + 12], 6, 0x655B59C3);
      d = _ii(d, a, b, c, x[i + 3], 10, 0x8F0CCC92);
      c = _ii(c, d, a, b, x[i + 10], 15, 0xFFEFF47D);
      b = _ii(b, c, d, a, x[i + 1], 21, 0x85845DD1);
      a = _ii(a, b, c, d, x[i + 8], 6, 0x6FA87E4F);
      d = _ii(d, a, b, c, x[i + 15], 10, 0xFE2CE6E0);
      c = _ii(c, d, a, b, x[i + 6], 15, 0xA3014314);
      b = _ii(b, c, d, a, x[i + 13], 21, 0x4E0811A1);
      a = _ii(a, b, c, d, x[i + 4], 6, 0xF7537E82);
      d = _ii(d, a, b, c, x[i + 11], 10, 0xBD3AF235);
      c = _ii(c, d, a, b, x[i + 2], 15, 0x2AD7D2BB);
      b = _ii(b, c, d, a, x[i + 9], 21, 0xEB86D391);

      a = _safeAdd(a, olda);
      b = _safeAdd(b, oldb);
      c = _safeAdd(c, oldc);
      d = _safeAdd(d, oldd);
    }
    return [a, b, c, d];
  }

  /// Convert an array of little-endian words to a hex string.
  static String _binl2hex(List<int> binarray) {
    const String hexTab = "0123456789abcdef";
    String str = "";
    for (int i = 0; i < binarray.length * 4; i++) {
      str += hexTab[(binarray[i >> 2] >> ((i % 4) * 8 + 4)) & 0xF] +
          hexTab[(binarray[i >> 2] >> ((i % 4) * 8)) & 0xF];
    }
    return str;
  }

  /// Convert an array of little-endian words to a base64 encoded string.
  static String _binl2b64(List<int> binarray) {
    const String tab = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    String str = "";
    for (int i = 0; i < binarray.length * 32; i += 6) {
      str += tab[((binarray[i >> 5] << (i % 32)) & 0x3F) |
          ((binarray[i >> 5 + 1] >> (32 - i % 32)) & 0x3F)];
    }
    return str;
  }

  /// Convert an 8-bit character string to a sequence of 16-word blocks, stored
  /// as an array, and append appropriate padding for MD5 calculation.
  static List<int> _str2binl(String str) {
    final int nblk = ((str.length + 8) >> 6) + 1; // number of 16-word blocks
    final List<int> blks = List.filled(nblk * 16, 0);
    
    for (int i = 0; i < str.length; i++) {
      blks[i >> 2] |= (str.codeUnitAt(i) & 0xFF) << ((i % 4) * 8);
    }
    blks[str.length >> 2] |= 0x80 << ((str.length % 4) * 8);
    blks[nblk * 16 - 2] = str.length * 8;
    return blks;
  }

  /// Calculate MD5 hash and return as hexadecimal string.
  static String hexMD5(String str) {
    return _binl2hex(_coreMD5(_str2binl(str)));
  }

  /// Calculate MD5 hash and return as base64 string.
  static String b64MD5(String str) {
    return _binl2b64(_coreMD5(_str2binl(str)));
  }

  /// Calculate MD5 hash (alias for hexMD5 for backward compatibility).
  static String calcMD5(String str) {
    return hexMD5(str);
  }

  /// Calculate MD5 hash from bytes and return as hexadecimal string.
  static String hexMD5FromBytes(Uint8List bytes) {
    final String str = String.fromCharCodes(bytes);
    return hexMD5(str);
  }

  /// Calculate MD5 hash from UTF-8 encoded string.
  static String hexMD5FromUtf8(String str) {
    final Uint8List bytes = Uint8List.fromList(utf8.encode(str));
    return hexMD5FromBytes(bytes);
  }
}
