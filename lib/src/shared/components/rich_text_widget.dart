import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/theme/app_color.dart';

/// 富文本显示组件
/// 支持 HTML 格式的富文本内容渲染
class RichTextWidget extends StatelessWidget {
  /// HTML 内容
  final String htmlContent;
  
  /// 基础文本样式
  final TextStyle? baseStyle;
  
  /// 是否启用链接点击
  final bool enableLinkTap;
  
  /// 链接点击回调
  final void Function(String? url)? onLinkTap;
  
  /// 自定义样式映射
  final Map<String, Style>? customStyles;
  
  /// 最大行数限制
  final int? maxLines;
  
  /// 文本对齐方式
  final TextAlign? textAlign;

  const RichTextWidget({
    super.key,
    required this.htmlContent,
    this.baseStyle,
    this.enableLinkTap = true,
    this.onLinkTap,
    this.customStyles,
    this.maxLines,
    this.textAlign,
  });

  /// 创建职位描述样式的富文本组件
  factory RichTextWidget.jobDescription({
    required String htmlContent,
    void Function(String? url)? onLinkTap,
    int? maxLines,
  }) {
    return RichTextWidget(
      htmlContent: htmlContent,
      baseStyle: const TextStyle(
        fontSize: 14,
        color: AppColors.textBody,
        height: 1.6,
      ),
      onLinkTap: onLinkTap,
      maxLines: maxLines,
      textAlign: TextAlign.left,
    );
  }

  /// 创建标题样式的富文本组件
  factory RichTextWidget.title({
    required String htmlContent,
    void Function(String? url)? onLinkTap,
  }) {
    return RichTextWidget(
      htmlContent: htmlContent,
      baseStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      onLinkTap: onLinkTap,
      textAlign: TextAlign.left,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Html(
      data: htmlContent,
      style: _buildHtmlStyles(),
      onLinkTap: enableLinkTap
        ? (url, attributes, element) {
            if (onLinkTap != null) {
              onLinkTap!(url);
            }
          }
        : null,
    );
  }

  /// 构建 HTML 样式映射
  Map<String, Style> _buildHtmlStyles() {
    final Map<String, Style> styles = {
      // 基础样式
      'body': Style(
        margin: Margins.zero,
        padding: HtmlPaddings.zero,
        fontSize: FontSize(baseStyle?.fontSize ?? 14),
        color: baseStyle?.color ?? AppColors.textBody,
        lineHeight: LineHeight(baseStyle?.height ?? 1.6),
        textAlign: textAlign ?? TextAlign.left,
        maxLines: maxLines,
      ),

      // 段落样式
      'p': Style(
        margin: Margins.only(bottom: 8.h),
        padding: HtmlPaddings.zero,
      ),

      // 标题样式
      'h1': Style(
        fontSize: FontSize(20.sp),
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
        margin: Margins.only(bottom: 12.h, top: 8.h),
      ),
      'h2': Style(
        fontSize: FontSize(18.sp),
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
        margin: Margins.only(bottom: 10.h, top: 6.h),
      ),
      'h3': Style(
        fontSize: FontSize(16.sp),
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
        margin: Margins.only(bottom: 8.h, top: 4.h),
      ),

      // 强调样式
      'strong': Style(
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      'b': Style(
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      'em': Style(
        fontStyle: FontStyle.italic,
      ),
      'i': Style(
        fontStyle: FontStyle.italic,
      ),
      'u': Style(
        textDecoration: TextDecoration.underline,
      ),

      // 链接样式
      'a': Style(
        color: AppColors.primary,
        textDecoration: TextDecoration.underline,
      ),

      // 列表样式
      'ul': Style(
        margin: Margins.only(bottom: 8.h, left: 16.w),
      ),
      'ol': Style(
        margin: Margins.only(bottom: 8.h, left: 16.w),
      ),
      'li': Style(
        margin: Margins.only(bottom: 4.h),
      ),

      // 引用样式
      'blockquote': Style(
        margin: Margins.only(left: 16.w, bottom: 8.h),
        padding: HtmlPaddings.only(left: 12.w),
        border: Border(
          left: BorderSide(
            color: AppColors.textSecondary,
            width: 3.w,
          ),
        ),
        backgroundColor: AppColors.pageBackground,
      ),

      // 代码样式
      'code': Style(
        backgroundColor: AppColors.pageBackground,
        color: AppColors.textPrimary,
        padding: HtmlPaddings.symmetric(horizontal: 4.w, vertical: 2.h),
      ),
      'pre': Style(
        backgroundColor: AppColors.pageBackground,
        color: AppColors.textPrimary,
        padding: HtmlPaddings.all(12.w),
        margin: Margins.only(bottom: 8.h),
      ),

      // 分割线样式
      'hr': Style(
        margin: Margins.symmetric(vertical: 16.h),
        border: Border(
          bottom: BorderSide(
            color: AppColors.textSecondary.withValues(alpha: 0.3),
            width: 1.w,
          ),
        ),
      ),
    };

    // 合并自定义样式
    if (customStyles != null) {
      styles.addAll(customStyles!);
    }

    return styles;
  }
}
