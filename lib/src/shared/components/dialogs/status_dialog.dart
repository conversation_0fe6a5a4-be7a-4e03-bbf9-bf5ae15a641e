import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/core/theme/dimens.dart';
import 'package:flutter_kit/src/shared/extensions/context_extensions.dart';

/// 状态弹窗类型
enum StatusDialogType {
  loading,  // 加载中
  success,  // 成功
  error,    // 错误
  timeout,  // 超时
}

/// 通用状态弹窗工具类
/// 支持加载中、成功、错误、超时四种状态
/// 弹窗显示时会阻止用户点击页面其他元素
class StatusDialog {
  static bool _isVisible = false;
  static Timer? _timeoutTimer;
  static Timer? _autoHideTimer;

  /// 显示加载中弹窗
  /// [context] 上下文
  /// [message] 显示文本，默认为"加载中..."
  /// [timeoutDuration] 超时时长，超时后自动显示超时弹窗
  /// [onTimeout] 超时回调
  static Future<void> showLoading({
    required BuildContext context,
    String message = '加载中...',
    Duration? timeoutDuration,
    VoidCallback? onTimeout,
  }) async {
    await _showDialog(
      context: context,
      type: StatusDialogType.loading,
      message: message,
      timeoutDuration: timeoutDuration,
      onTimeout: onTimeout,
    );
  }

  /// 显示成功弹窗
  /// [context] 上下文
  /// [message] 显示文本，默认为"操作成功"
  /// [autoHideDuration] 自动隐藏时长，默认2秒后自动隐藏
  /// [onAutoHide] 自动隐藏回调
  static Future<void> showSuccess({
    required BuildContext context,
    String message = '操作成功',
    Duration autoHideDuration = const Duration(seconds: 2),
    VoidCallback? onAutoHide,
  }) async {
    await _showDialog(
      context: context,
      type: StatusDialogType.success,
      message: message,
      autoHideDuration: autoHideDuration,
      onAutoHide: onAutoHide,
    );
  }

  /// 显示错误弹窗
  /// [context] 上下文
  /// [message] 显示文本，默认为"操作失败"
  /// [autoHideDuration] 自动隐藏时长，默认3秒后自动隐藏
  /// [onAutoHide] 自动隐藏回调
  static Future<void> showError({
    required BuildContext context,
    String message = '操作失败',
    Duration autoHideDuration = const Duration(seconds: 3),
    VoidCallback? onAutoHide,
  }) async {
    await _showDialog(
      context: context,
      type: StatusDialogType.error,
      message: message,
      autoHideDuration: autoHideDuration,
      onAutoHide: onAutoHide,
    );
  }

  /// 显示超时弹窗
  /// [context] 上下文
  /// [message] 显示文本，默认为"请求超时"
  /// [autoHideDuration] 自动隐藏时长，默认3秒后自动隐藏
  /// [onAutoHide] 自动隐藏回调
  static Future<void> showTimeout({
    required BuildContext context,
    String message = '请求超时',
    Duration autoHideDuration = const Duration(seconds: 3),
    VoidCallback? onAutoHide,
  }) async {
    await _showDialog(
      context: context,
      type: StatusDialogType.timeout,
      message: message,
      autoHideDuration: autoHideDuration,
      onAutoHide: onAutoHide,
    );
  }

  /// 隐藏弹窗
  static void hide({required BuildContext context}) {
    if (_isVisible) {
      _isVisible = false;
      _cancelTimers();
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    }
  }

  /// 检查弹窗是否正在显示
  static bool get isVisible => _isVisible;

  /// 内部方法：显示弹窗
  static Future<void> _showDialog({
    required BuildContext context,
    required StatusDialogType type,
    required String message,
    Duration? timeoutDuration,
    Duration? autoHideDuration,
    VoidCallback? onTimeout,
    VoidCallback? onAutoHide,
  }) async {
    // 如果已经有弹窗显示，先隐藏
    if (_isVisible) {
      hide(context: context);
      // 等待一帧确保弹窗完全隐藏
      await Future.delayed(const Duration(milliseconds: 100));
    }

    _isVisible = true;

    // 设置超时定时器（仅对加载中状态有效）
    if (type == StatusDialogType.loading && timeoutDuration != null) {
      _timeoutTimer = Timer(timeoutDuration, () {
        if (_isVisible) {
          onTimeout?.call();
          showTimeout(context: context);
        }
      });
    }

    // 设置自动隐藏定时器（对成功、错误、超时状态有效）
    if (autoHideDuration != null && type != StatusDialogType.loading) {
      _autoHideTimer = Timer(autoHideDuration, () {
        if (_isVisible) {
          hide(context: context);
          onAutoHide?.call();
        }
      });
    }

    return showDialog(
      context: context,
      barrierDismissible: false,
      useRootNavigator: false,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: _buildDialogContent(type, message),
          ),
        );
      },
    );
  }

  /// 构建弹窗内容
  static Widget _buildDialogContent(StatusDialogType type, String message) {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          minWidth: 120.w,
          maxWidth: 200.w,
          minHeight: 120.h,
        ),
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: AppColors.alpha_20_black,
              blurRadius: 10.r,
              offset: Offset(0, 4.h),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildIcon(type),
            SizedBox(height: 16.h),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.color_333333,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建图标
  static Widget _buildIcon(StatusDialogType type) {
    switch (type) {
      case StatusDialogType.loading:
        return SizedBox(
          width: 32.w,
          height: 32.w,
          child: CircularProgressIndicator(
            strokeWidth: 3.w,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        );
      case StatusDialogType.success:
        return Container(
          width: 32.w,
          height: 32.w,
          decoration: BoxDecoration(
            color: Colors.green,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check,
            color: AppColors.white,
            size: 20.w,
          ),
        );
      case StatusDialogType.error:
        return Container(
          width: 32.w,
          height: 32.w,
          decoration: BoxDecoration(
            color: AppColors.primary,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.close,
            color: AppColors.white,
            size: 20.w,
          ),
        );
      case StatusDialogType.timeout:
        return Container(
          width: 32.w,
          height: 32.w,
          decoration: BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.access_time,
            color: AppColors.white,
            size: 20.w,
          ),
        );
    }
  }

  /// 取消所有定时器
  static void _cancelTimers() {
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
    _autoHideTimer?.cancel();
    _autoHideTimer = null;
  }
}
