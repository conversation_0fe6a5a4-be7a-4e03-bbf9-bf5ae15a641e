import 'package:flutter/material.dart';
import 'package:flutter_kit/src/core/theme/dimens.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';

class Input extends StatefulWidget {
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final bool readOnly;
  final String? hintText;
  final TextStyle? hintStyle;
  final TextStyle? style;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Color? cursorColor;
  final bool? filled;
  final bool isPassword;
  final int? minLines;
  final bool isBorderless;
  final TextInputType? keyboardType;
  final Color? fillColor;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final bool enabled;
  final int? maxLines;
  final TextInputAction? textInputAction;
  final List<String>? autofillHints;
  final String? labelText;
  final TextCapitalization? textCapitalization;
  final bool expands;

  const Input({
    super.key,
    this.focusNode,
    required this.controller,
    this.hintText,
    this.hintStyle,
    this.style,
    this.prefixIcon,
    this.suffixIcon,
    this.cursorColor,
    this.filled,
    this.isPassword = false,
    this.minLines,
    this.isBorderless = false,
    this.keyboardType,
    this.fillColor,
    this.validator,
    this.onChanged,
    this.readOnly = false,
    this.enabled = true,
    this.maxLines,
    this.textInputAction,
    this.autofillHints,
    this.onSubmitted,
    this.labelText,
    this.textCapitalization,
    this.expands = false,
  });

  @override
  State<Input> createState() => _InputState();
}

class _InputState extends State<Input> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// 强制请求焦点的方法，确保键盘弹出
  void _requestFocusWithRetry() {
    // 检查是否已经有焦点，如果有就不需要重新请求
    if (_focusNode.hasFocus) {
      return;
    }

    // 方法1: 立即请求焦点
    _focusNode.requestFocus();

    // 方法2: 使用 FocusScope 作为备用
    FocusScope.of(context).requestFocus(_focusNode);

    // 方法3: 延迟重试，确保焦点获取成功
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted && !_focusNode.hasFocus) {
        _focusNode.requestFocus();
        FocusScope.of(context).requestFocus(_focusNode);
      }
    });

    // 方法4: 再次延迟重试
    Future.delayed(const Duration(milliseconds: 150), () {
      if (mounted && !_focusNode.hasFocus) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null) ...[
          Text(
            widget.labelText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: Dimens.minSpacing),
        ],
        // 圆角浅白色背景输入框
        GestureDetector(
          onTap: () {
            // 每次点击都强制请求焦点，确保键盘弹出
            _requestFocusWithRetry();
          },
          behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8), // 浅白色背景
              borderRadius: BorderRadius.circular(12.0), // 圆角
              border: Border.all(
                color: Colors.transparent, // 透明边框
                width: 1.0,
              ),
            ),
            child: TextFormField(
              expands: widget.expands,
              autofillHints: widget.autofillHints,
              controller: widget.controller,
              obscureText: widget.isPassword,
              cursorColor: widget.cursorColor ?? AppColors.primary,
              readOnly: widget.readOnly,
              focusNode: _focusNode,
              style: widget.style ?? Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
              minLines: widget.isPassword ? 1 : widget.minLines,
              maxLines: widget.isPassword ? 1 : widget.maxLines,
              keyboardType: widget.keyboardType,
              textInputAction: widget.textInputAction,
              onFieldSubmitted: widget.onSubmitted,
              textCapitalization: widget.textCapitalization ?? TextCapitalization.sentences,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
                hintText: widget.hintText,
                hintStyle: widget.hintStyle ?? TextStyle(
                  color: AppColors.textSecondary.withValues(alpha: 0.6),
                  fontSize: 16.0,
                ),
                filled: false,
                prefixIcon: widget.prefixIcon,
                suffixIcon: widget.suffixIcon,
                enabled: widget.enabled,
                // 移除所有边框，使用容器的圆角背景
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
              ),
              onChanged: widget.onChanged,
              validator: widget.validator,
              onTap: () {
                // TextField 被点击时也强制请求焦点
                _requestFocusWithRetry();
              },
            ),
          ),
        ),
      ],
    );
  }
}
