import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_color.dart';

/// 通用AppBar
/// 参考joke_fun项目的简洁设计
AppBar commonAppBar({
  Color statusBarColor = Colors.transparent,
  Color? backgroundColor,
  bool iconDark = true,
  PreferredSizeWidget? bottom,
}) {
  return AppBar(
    elevation: 0,
    toolbarHeight: 0,
    scrolledUnderElevation: 0,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: iconDark ? Brightness.dark : Brightness.light,
    ),
    backgroundColor: backgroundColor ?? Colors.white,
    bottom: bottom,
  );
}

/// 通用标题栏
PreferredSizeWidget? commonTitleBar({
  required BuildContext context,
  String leftIcon = 'ic_back',
  String title = '',
  String? rightIcon,
  String? rightText,
  Widget? rightWidget,
  GestureTapCallback? leftClick,
  GestureTapCallback? rightClick,
  Color? contentColor,
  Color? backgroundColor,
}) {
  Widget? right;
  if (rightText != null || rightIcon != null) {
    right = Container(
      width: 160.w,
      alignment: Alignment.centerRight,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: rightClick,
        child: (rightIcon != null)
            ? Icon(Icons.more_vert, size: 24.w, color: contentColor ?? AppColors.color_333333)
            : Text(
                rightText!,
                style: TextStyle(
                  color: contentColor ?? AppColors.color_333333,
                  fontSize: 16.sp,
                ),
              ),
      ),
    );
  } else {
    right = (rightWidget != null)
        ? GestureDetector(
            onTap: rightClick,
            child: Container(
              width: 160.w,
              alignment: Alignment.centerRight,
              child: rightWidget,
            ),
          )
        : Container(width: 160.w);
  }

  return PreferredSize(
    preferredSize: Size(double.infinity, 56.h),
    child: Container(
      height: 56.h,
      color: backgroundColor ?? Colors.transparent,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: 16.w),
          Container(
            alignment: Alignment.centerLeft,
            width: 160.w,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: leftClick ?? () => Navigator.of(context).pop(),
              child: Icon(
                Icons.arrow_back_ios,
                size: 20.w,
                color: contentColor ?? AppColors.color_333333,
              ),
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  color: contentColor ?? AppColors.color_333333,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          right,
          SizedBox(width: 16.w),
        ],
      ),
    ),
  );
}

/// 更新状态栏颜色
void updateStatusBarColor(Color color, bool iconDark) {
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: color,
    statusBarIconBrightness: iconDark ? Brightness.dark : Brightness.light,
  ));
}
