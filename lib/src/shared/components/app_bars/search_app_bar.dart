import 'package:flutter/material.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/shared/components/app_bars/app_bar_config.dart';
import 'package:flutter_kit/src/shared/components/app_bars/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 搜索AppBar组件，专门用于带搜索功能的页面
class SearchAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? location;
  final String? searchHint;
  final VoidCallback? onLocationTap;
  final VoidCallback? onSearchTap;
  final List<Widget>? actions;
  final Widget? leading;

  const SearchAppBar({
    super.key,
    this.location,
    this.searchHint,
    this.onLocationTap,
    this.onSearchTap,
    this.actions,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      config: AppBarConfig.transparent(
        titleWidget: _buildSearchContent(),
        actions: actions,
        leading: leading,
      ),
    );
  }

  Widget _buildSearchContent() {
    return Container(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 12.w),
      child: Row(
        children: [
          // 位置选择
          GestureDetector(
            onTap: onLocationTap,
            child: Row(
              children: [
                Icon(Icons.location_on,
                    size: 14.w, color: AppColors.color_333333),
                SizedBox(width: 2.w),
                Text(
                  location ?? '汕头',
                  style: TextStyle(
                    fontSize: 13.w,
                    fontWeight: FontWeight.bold,
                    color: AppColors.color_333333,
                  ),
                ),
                SizedBox(width: 0.w),
                Icon(Icons.arrow_drop_down,
                    size: 20.w, color: AppColors.color_333333),
              ],
            ),
          ),
          SizedBox(width: 6.w),
          // 搜索框
          Expanded(
            child: GestureDetector(
              onTap: onSearchTap,
              child: Card(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.w)),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                  child: Row(
                    children: [
                      Icon(Icons.search,
                          size: 20.w, color: AppColors.color_333333),
                      SizedBox(width: 6),
                      Expanded(
                          child: Text(
                        searchHint ?? '搜索职位名称',
                        style: TextStyle(
                            fontSize: 15.w, color: AppColors.color_cccccc),
                      )),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + 12.w);
}

/// 搜索AppBar构建器
class SearchAppBarBuilder {
  /// 构建带位置和搜索的AppBar
  static PreferredSizeWidget build({
    String? location,
    String? searchHint,
    VoidCallback? onLocationTap,
    VoidCallback? onSearchTap,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return SearchAppBar(
      location: location,
      searchHint: searchHint,
      onLocationTap: onLocationTap,
      onSearchTap: onSearchTap,
      actions: actions,
      leading: leading,
    );
  }
} 