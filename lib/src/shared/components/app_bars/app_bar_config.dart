import 'package:flutter/material.dart';

/// AppBar配置类，用于统一管理AppBar的样式和行为
class AppBarConfig {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final double? elevation;
  final bool? centerTitle;
  final double? toolbarHeight;
  final PreferredSizeWidget? bottom;
  final bool? primary;
  final bool excludeHeaderSemantics;
  final double? titleSpacing;
  final double? leadingWidth;
  final TextStyle? titleTextStyle;
  final IconThemeData? iconTheme;
  final IconThemeData? actionsIconTheme;
  final Color? foregroundColor;
  final ShapeBorder? shape;
  final double? scrolledUnderElevation;
  final Color? shadowColor;
  final Color? surfaceTintColor;

  const AppBarConfig({
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.elevation,
    this.centerTitle,
    this.toolbarHeight,
    this.bottom,
    this.primary,
    this.excludeHeaderSemantics = false,
    this.titleSpacing,
    this.leadingWidth,
    this.titleTextStyle,
    this.iconTheme,
    this.actionsIconTheme,
    this.foregroundColor,
    this.shape,
    this.scrolledUnderElevation,
    this.shadowColor,
    this.surfaceTintColor,
  }) : assert(title == null || titleWidget == null, '不能同时设置title和titleWidget');

  /// 创建默认AppBar配置
  factory AppBarConfig.defaultConfig({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return AppBarConfig(
      title: title,
      titleWidget: titleWidget,
      actions: actions,
      leading: leading,
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: false,
    );
  }

  /// 创建透明AppBar配置
  factory AppBarConfig.transparent({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return AppBarConfig(
      title: title,
      titleWidget: titleWidget,
      actions: actions,
      leading: leading,
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: false,
    );
  }

  /// 创建带搜索框的AppBar配置
  factory AppBarConfig.withSearch({
    required Widget searchWidget,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return AppBarConfig(
      titleWidget: searchWidget,
      actions: actions,
      leading: leading,
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: false,
    );
  }

  /// 复制并修改配置
  AppBarConfig copyWith({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
    bool? automaticallyImplyLeading,
    Color? backgroundColor,
    double? elevation,
    bool? centerTitle,
    double? toolbarHeight,
    PreferredSizeWidget? bottom,
    bool? primary,
    bool? excludeHeaderSemantics,
    double? titleSpacing,
    double? leadingWidth,
    TextStyle? titleTextStyle,
    IconThemeData? iconTheme,
    IconThemeData? actionsIconTheme,
    Color? foregroundColor,
    ShapeBorder? shape,
    double? scrolledUnderElevation,
    Color? shadowColor,
    Color? surfaceTintColor,
  }) {
    return AppBarConfig(
      title: title ?? this.title,
      titleWidget: titleWidget ?? this.titleWidget,
      actions: actions ?? this.actions,
      leading: leading ?? this.leading,
      automaticallyImplyLeading: automaticallyImplyLeading ?? this.automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      elevation: elevation ?? this.elevation,
      centerTitle: centerTitle ?? this.centerTitle,
      toolbarHeight: toolbarHeight ?? this.toolbarHeight,
      bottom: bottom ?? this.bottom,
      primary: primary ?? this.primary,
      excludeHeaderSemantics: excludeHeaderSemantics ?? this.excludeHeaderSemantics,
      titleSpacing: titleSpacing ?? this.titleSpacing,
      leadingWidth: leadingWidth ?? this.leadingWidth,
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      iconTheme: iconTheme ?? this.iconTheme,
      actionsIconTheme: actionsIconTheme ?? this.actionsIconTheme,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      shape: shape ?? this.shape,
      scrolledUnderElevation: scrolledUnderElevation ?? this.scrolledUnderElevation,
      shadowColor: shadowColor ?? this.shadowColor,
      surfaceTintColor: surfaceTintColor ?? this.surfaceTintColor,
    );
  }
} 