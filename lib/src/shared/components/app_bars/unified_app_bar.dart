import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_color.dart';

/// 统一的AppBar系统
class UnifiedAppBar {
  /// 1. 通用AppBar - 带返回按钮和居中标题
  static AppBar generic({
    Color statusBarColor = Colors.transparent,
    Color? backgroundColor,
    bool iconDark = true,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      elevation: 0,
      toolbarHeight: 0,
      scrolledUnderElevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: iconDark ? Brightness.dark : Brightness.light,
      ),
      backgroundColor: backgroundColor ?? Colors.white,
      bottom: bottom,
    );
  }

  /// 2. 自定义AppBar - 支持单页面自定义视图
  static PreferredSizeWidget custom({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
    Color? backgroundColor,
    Color? contentColor,
    VoidCallback? onLeadingPressed,
  }) {
    return PreferredSize(
      preferredSize: Size(double.infinity, 88.w),
      child: Container(
        height: 88.w,
        color: backgroundColor ?? Colors.transparent,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 32.w),
            // 左侧按钮区域
            Container(
              alignment: Alignment.centerLeft,
              width: 160.w,
              child: leading ?? 
                (onLeadingPressed != null 
                  ? GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: onLeadingPressed,
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: 20.w,
                        color: contentColor ?? AppColors.color_f02e4b,
                      ),
                    )
                  : null),
            ),
            // 中间标题区域
            Expanded(
              child: Center(
                child: titleWidget ?? 
                  (title != null 
                    ? Text(
                        title,
                        style: TextStyle(
                          color: contentColor ?? AppColors.color_f02e4b,
                          fontSize: 36.w,
                          fontWeight: FontWeight.w500,
                        ),
                      )
                    : const SizedBox()),
              ),
            ),
            // 右侧操作区域
            Container(
              width: 160.w,
              alignment: Alignment.centerRight,
              child: actions != null && actions.isNotEmpty
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: actions,
                  )
                : const SizedBox(),
            ),
            SizedBox(width: 32.w),
          ],
        ),
      ),
    );
  }

  /// 3. 左返回按钮 + 居中标题 + 右图标
  static PreferredSizeWidget withRightIcon({
    required String title,
    required IconData rightIcon,
    VoidCallback? onBackPressed,
    VoidCallback? onRightPressed,
    Color? backgroundColor,
    Color? contentColor,
  }) {
    return PreferredSize(
      preferredSize: Size(double.infinity, 88.w),
      child: Container(
        height: 88.w,
        color: backgroundColor ?? Colors.transparent,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 32.w),
            // 左侧返回按钮
            Container(
              alignment: Alignment.centerLeft,
              width: 160.w,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onBackPressed,
                child: Icon(
                  Icons.arrow_back_ios,
                  size: 20.w,
                  color: contentColor ?? AppColors.color_f02e4b,
                ),
              ),
            ),
            // 中间标题
            Expanded(
              child: Center(
                child: Text(
                  title,
                  style: TextStyle(
                    color: contentColor ?? AppColors.color_f02e4b,
                    fontSize: 36.w,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // 右侧图标
            Container(
              width: 160.w,
              alignment: Alignment.centerRight,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onRightPressed,
                child: Icon(
                  rightIcon,
                  size: 20.w,
                  color: contentColor ?? AppColors.color_f02e4b,
                ),
              ),
            ),
            SizedBox(width: 32.w),
          ],
        ),
      ),
    );
  }

  /// 4. 透明AppBar - 初始透明，滚动时显示
  static Widget scrollableTransparent({
    required ScrollController scrollController,
    required String title,
    VoidCallback? onBackPressed,
    Color? backgroundColor,
    Color? contentColor,
  }) {
    return AnimatedBuilder(
      animation: scrollController,
      builder: (context, child) {
        // 根据滚动位置计算透明度
        double opacity = (scrollController.offset / 100).clamp(0.0, 1.0);
        
        // 获取系统状态栏高度
        final statusBarHeight = MediaQuery.of(context).viewPadding.top;
        final appBarHeight = 44.w; // AppBar内容高度
        final totalHeight = statusBarHeight + appBarHeight;

        return Container(
          height: totalHeight,
          decoration: BoxDecoration(
            color: (backgroundColor ?? Colors.white).withOpacity(opacity),
          ),
          child: Padding(
            padding: EdgeInsets.only(top: statusBarHeight),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 16.w),
                // 左侧返回按钮
                Container(
                  alignment: Alignment.centerLeft,
                  width: 44.w,
                  height: 44.w,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: onBackPressed,
                    child: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                      color: (contentColor ?? AppColors.color_f02e4b).withOpacity(opacity),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 中间标题（只在滚动时显示）
                Expanded(
                  child: Center(
                    child: Opacity(
                      opacity: opacity,
                      child: Text(
                        title,
                        style: TextStyle(
                          color: contentColor ?? AppColors.color_f02e4b,
                          fontSize: 18.w,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 右侧占位，保持标题居中
                Container(width: 44.w),
                SizedBox(width: 16.w),
              ],
            ),
          ),
        );
      },
    );
  }



  /// 4. 中间文字显示 - 初始透明，滚动时显示
  static Widget scrollableTitleTransparent({
    required ScrollController scrollController,
    required String title,
    VoidCallback? onBackPressed,
    Color? backgroundColor,
    Color? contentColor,
    Color? iconColor,
  }) {
    return AnimatedBuilder(
      animation: scrollController,
      builder: (context, child) {
        // 根据滚动位置计算透明度
        double opacity = (scrollController.offset / 100).clamp(0.0, 1.0);

        // 获取系统状态栏高度
        final statusBarHeight = MediaQuery.of(context).viewPadding.top;
        final appBarHeight = 44.w; // AppBar内容高度
        final totalHeight = statusBarHeight + appBarHeight;

        return Container(
          height: totalHeight,
          decoration: BoxDecoration(
            color: backgroundColor
          ),
          child: Padding(
            padding: EdgeInsets.only(top: statusBarHeight),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 16.w),
                // 左侧返回按钮
                Container(
                  alignment: Alignment.centerLeft,
                  width: 44.w,
                  height: 44.w,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: onBackPressed,
                    child: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                      color: iconColor ?? AppColors.white
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 中间标题（只在滚动时显示）
                Expanded(
                  child: Center(
                    child: Opacity(
                      opacity: opacity,
                      child: Text(
                        title,
                        style: TextStyle(
                          color: contentColor ?? AppColors.color_f02e4b,
                          fontSize: 18.w,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 右侧占位，保持标题居中
                Container(width: 44.w),
                SizedBox(width: 16.w),
              ],
            ),
          ),
        );
      },
    );
  }


  /// 4. 只有返回按钮显示 - 初始透明，滚动时显示
  static Widget scrollableIconTransparent({
    required ScrollController scrollController,
    required String title,
    VoidCallback? onBackPressed,
    Color? backgroundColor,
    Color? contentColor,
    Color? iconColor,
  }) {
    return AnimatedBuilder(
      animation: scrollController,
      builder: (context, child) {
        // 根据滚动位置计算透明度
        double opacity = (scrollController.offset / 100).clamp(0.0, 1.0);

        // 获取系统状态栏高度
        final statusBarHeight = MediaQuery.of(context).viewPadding.top;
        final appBarHeight = 44.w; // AppBar内容高度
        final totalHeight = statusBarHeight + appBarHeight;

        return Container(
          height: totalHeight,
          decoration: BoxDecoration(
            color: (backgroundColor ?? Colors.white).withOpacity(opacity),
          ),
          child: Padding(
            padding: EdgeInsets.only(top: statusBarHeight),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 16.w),
                // 左侧返回按钮
                Container(
                  alignment: Alignment.centerLeft,
                  width: 44.w,
                  height: 44.w,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: onBackPressed,
                    child: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                      color: iconColor ?? AppColors.color_white
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 中间标题（只在滚动时显示）
                Expanded(
                  child: Center(
                    child: Opacity(
                      opacity: opacity,
                      child: Text(
                        title,
                        style: TextStyle(
                          color: contentColor ?? AppColors.color_f02e4b,
                          fontSize: 18.w,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 右侧占位，保持标题居中
                Container(width: 44.w),
                SizedBox(width: 16.w),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 4. 只有返回按钮显示 - 返回按钮、文字
  static Widget customTitleBar({
    required String title,
    VoidCallback? onBackPressed,
    Color? backgroundColor,
    Color? contentColor,
    Color? iconColor,
  }) {
    return Builder(
      builder: (context) {
        // 获取系统状态栏高度
        final statusBarHeight = MediaQuery.of(context).viewPadding.top;
        final appBarHeight = 44.w; // AppBar内容高度
        final totalHeight = statusBarHeight + appBarHeight;

        return Container(
          height: totalHeight,
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white,
          ),
          child: Padding(
            padding: EdgeInsets.only(top: statusBarHeight),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 16.w),
                // 左侧返回按钮
                Container(
                  alignment: Alignment.centerLeft,
                  width: 44.w,
                  height: 44.w,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: onBackPressed,
                    child: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                      color: iconColor ?? AppColors.color_666666,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 中间标题
                Expanded(
                  child: Center(
                    child: Text(
                      title,
                      style: TextStyle(
                        color: contentColor ?? AppColors.textPrimary,
                        fontSize: 18.w,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // 右侧占位，保持标题居中
                Container(width: 44.w),
                SizedBox(width: 16.w),
              ],
            ),
          ),
        );
      },
    );
  }



  /// 5. 固定标题栏  只有返回按钮、标题
  static PreferredSizeWidget customTitlebar({
    required String title,
    VoidCallback? onBackPressed,
    Color? backgroundColor,
    Color? contentColor,
    Color? iconColor,
  }) {
    // 获取系统状态栏高度和AppBar高度
    final appBarHeight = 44.0; // 使用固定值，因为在静态方法中无法访问.w
    final totalHeight = kToolbarHeight + appBarHeight; // 使用Flutter的标准工具栏高度

    return PreferredSize(
      preferredSize: Size.fromHeight(totalHeight),
      child: Builder(
        builder: (context) {
          final statusBarHeight = MediaQuery.of(context).viewPadding.top;
          final actualTotalHeight = statusBarHeight + appBarHeight;

          return Container(
            height: actualTotalHeight,
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.white,
            ),
            child: Padding(
              padding: EdgeInsets.only(top: statusBarHeight),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(width: 16.0),
                  // 左侧返回按钮
                  Container(
                    alignment: Alignment.centerLeft,
                    width: 44.0,
                    height: 44.0,
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: onBackPressed,
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: 20.0,
                        color: iconColor ?? AppColors.color_666666,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.0),
                  // 中间标题
                  Expanded(
                    child: Center(
                      child: Text(
                        title,
                        style: TextStyle(
                          color: contentColor ?? AppColors.textPrimary,
                          fontSize: 18.0,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.0),
                  // 右侧占位，保持标题居中
                  Container(width: 44.0),
                  SizedBox(width: 16.0),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 更新状态栏颜色
  static void updateStatusBarColor(Color color, bool iconDark) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: color,
      statusBarIconBrightness: iconDark ? Brightness.dark : Brightness.light,
    ));
  }
}
