import 'package:flutter/material.dart';
import 'package:flutter_kit/src/shared/components/app_bars/app_bar_config.dart';

/// 自定义AppBar组件，使用AppBarConfig来统一管理样式和行为
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final AppBarConfig config;

  const CustomAppBar({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: config.titleWidget ?? (config.title != null ? Text(config.title!) : null),
      actions: config.actions,
      leading: config.leading,
      automaticallyImplyLeading: config.automaticallyImplyLeading,
      backgroundColor: config.backgroundColor,
      elevation: config.elevation,
      centerTitle: config.centerTitle,
      toolbarHeight: config.toolbarHeight,
      bottom: config.bottom,
      primary: config.primary ?? true,
      excludeHeaderSemantics: config.excludeHeaderSemantics,
      titleSpacing: config.titleSpacing,
      leadingWidth: config.leadingWidth,
      titleTextStyle: config.titleTextStyle,
      iconTheme: config.iconTheme,
      actionsIconTheme: config.actionsIconTheme,
      foregroundColor: config.foregroundColor,
      shape: config.shape,
      scrolledUnderElevation: config.scrolledUnderElevation,
      shadowColor: config.shadowColor,
      surfaceTintColor: config.surfaceTintColor,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(config.toolbarHeight ?? kToolbarHeight);
}

/// 便捷的AppBar构建方法
class AppBarBuilder {
  /// 构建默认AppBar
  static PreferredSizeWidget buildDefault({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return CustomAppBar(
      config: AppBarConfig.defaultConfig(
        title: title,
        titleWidget: titleWidget,
        actions: actions,
        leading: leading,
      ),
    );
  }

  /// 构建透明AppBar
  static PreferredSizeWidget buildTransparent({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return CustomAppBar(
      config: AppBarConfig.transparent(
        title: title,
        titleWidget: titleWidget,
        actions: actions,
        leading: leading,
      ),
    );
  }

  /// 构建带搜索框的AppBar
  static PreferredSizeWidget buildWithSearch({
    required Widget searchWidget,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return CustomAppBar(
      config: AppBarConfig.withSearch(
        searchWidget: searchWidget,
        actions: actions,
        leading: leading,
      ),
    );
  }

  /// 构建自定义AppBar
  static PreferredSizeWidget buildCustom(AppBarConfig config) {
    return CustomAppBar(config: config);
  }
} 