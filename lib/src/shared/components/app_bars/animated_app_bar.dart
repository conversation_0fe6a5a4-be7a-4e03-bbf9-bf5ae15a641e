import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_kit/src/shared/components/app_bars/app_bar_config.dart';
import 'package:flutter_kit/src/shared/components/app_bars/custom_app_bar.dart';

/// 动画AppBar配置
class AnimatedAppBarConfig {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final Color? transparentColor;
  final double? elevation;
  final double? transparentElevation;
  final bool centerTitle;
  final double? toolbarHeight;
  final TextStyle? titleTextStyle;
  final TextStyle? transparentTitleTextStyle;
  final IconThemeData? iconTheme;
  final IconThemeData? transparentIconTheme;
  final IconThemeData? actionsIconTheme;
  final IconThemeData? transparentActionsIconTheme;
  final double scrollThreshold; // 滚动阈值，达到此值时开始显示AppBar
  final Curve animationCurve; // 动画曲线
  final Duration animationDuration; // 动画时长

  const AnimatedAppBarConfig({
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.transparentColor,
    this.elevation,
    this.transparentElevation,
    this.centerTitle = false,
    this.toolbarHeight,
    this.titleTextStyle,
    this.transparentTitleTextStyle,
    this.iconTheme,
    this.transparentIconTheme,
    this.actionsIconTheme,
    this.transparentActionsIconTheme,
    this.scrollThreshold = 50.0,
    this.animationCurve = Curves.easeInOut,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  /// 创建默认动画AppBar配置
  factory AnimatedAppBarConfig.defaultConfig({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
  }) {
    return AnimatedAppBarConfig(
      title: title,
      titleWidget: titleWidget,
      actions: actions,
      leading: leading,
      backgroundColor: Colors.white,
      transparentColor: Colors.transparent,
      elevation: 4.0,
      transparentElevation: 0.0,
      centerTitle: false,
    );
  }

  /// 创建透明到不透明的动画AppBar配置
  factory AnimatedAppBarConfig.transparentToOpaque({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
    Color? backgroundColor,
    Color? transparentColor,
  }) {
    return AnimatedAppBarConfig(
      title: title,
      titleWidget: titleWidget,
      actions: actions,
      leading: leading,
      backgroundColor: backgroundColor ?? Colors.white,
      transparentColor: transparentColor ?? Colors.transparent,
      elevation: 4.0,
      transparentElevation: 0.0,
      centerTitle: false,
    );
  }
}

/// 动画AppBar组件
class AnimatedAppBar extends StatefulWidget implements PreferredSizeWidget {
  final AnimatedAppBarConfig config;
  final ScrollController? scrollController;
  final double? scrollOffset;

  const AnimatedAppBar({
    super.key,
    required this.config,
    this.scrollController,
    this.scrollOffset,
  });

  @override
  State<AnimatedAppBar> createState() => _AnimatedAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(config.toolbarHeight ?? kToolbarHeight);
}

class _AnimatedAppBarState extends State<AnimatedAppBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  double _lastScrollOffset = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.config.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.config.animationCurve,
    ));

    // 监听滚动控制器
    if (widget.scrollController != null) {
      widget.scrollController!.addListener(_onScroll);
    }
  }

  @override
  void didUpdateWidget(AnimatedAppBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果滚动控制器发生变化，重新监听
    if (oldWidget.scrollController != widget.scrollController) {
      oldWidget.scrollController?.removeListener(_onScroll);
      widget.scrollController?.addListener(_onScroll);
    }
  }

  void _onScroll() {
    if (widget.scrollController == null) return;
    
    final offset = widget.scrollController!.offset;
    final threshold = widget.config.scrollThreshold;
    
    if (offset > threshold) {
      // 滚动超过阈值，显示AppBar
      if (_animationController.value < 1.0) {
        _animationController.forward();
      }
    } else {
      // 滚动未超过阈值，隐藏AppBar
      if (_animationController.value > 0.0) {
        _animationController.reverse();
      }
    }
    
    _lastScrollOffset = offset;
  }

  @override
  void dispose() {
    widget.scrollController?.removeListener(_onScroll);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return _buildAnimatedAppBar();
      },
    );
  }

  Widget _buildAnimatedAppBar() {
    final animationValue = _animation.value;
    
    // 计算动画值
    final backgroundColor = Color.lerp(
      widget.config.transparentColor ?? Colors.transparent,
      widget.config.backgroundColor ?? Colors.white,
      animationValue,
    );
    
    final elevation = lerpDouble(
      widget.config.transparentElevation ?? 0.0,
      widget.config.elevation ?? 4.0,
      animationValue,
    );
    
    final titleTextStyle = widget.config.titleTextStyle != null && 
                          widget.config.transparentTitleTextStyle != null
        ? TextStyle.lerp(
            widget.config.transparentTitleTextStyle!,
            widget.config.titleTextStyle!,
            animationValue,
          )
        : widget.config.titleTextStyle;
    
    final iconTheme = widget.config.iconTheme != null && 
                     widget.config.transparentIconTheme != null
        ? IconThemeData.lerp(
            widget.config.transparentIconTheme!,
            widget.config.iconTheme!,
            animationValue,
          )
        : widget.config.iconTheme;
    
    final actionsIconTheme = widget.config.actionsIconTheme != null && 
                           widget.config.transparentActionsIconTheme != null
        ? IconThemeData.lerp(
            widget.config.transparentActionsIconTheme!,
            widget.config.actionsIconTheme!,
            animationValue,
          )
        : widget.config.actionsIconTheme;

    // 构建AppBar配置
    final appBarConfig = AppBarConfig(
      title: widget.config.title,
      titleWidget: widget.config.titleWidget,
      actions: widget.config.actions,
      leading: widget.config.leading,
      backgroundColor: backgroundColor,
      elevation: elevation,
      centerTitle: widget.config.centerTitle,
      toolbarHeight: widget.config.toolbarHeight,
      titleTextStyle: titleTextStyle,
      iconTheme: iconTheme,
      actionsIconTheme: actionsIconTheme,
    );

    return CustomAppBar(config: appBarConfig);
  }
}

/// 动画AppBar构建器
class AnimatedAppBarBuilder {
  /// 构建默认动画AppBar
  static PreferredSizeWidget buildDefault({
    required ScrollController scrollController,
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
    double scrollThreshold = 50.0,
  }) {
    return AnimatedAppBar(
      config: AnimatedAppBarConfig.defaultConfig(
        title: title,
        titleWidget: titleWidget,
        actions: actions,
        leading: leading,
      ).copyWith(scrollThreshold: scrollThreshold),
      scrollController: scrollController,
    );
  }

  /// 构建透明到不透明的动画AppBar
  static PreferredSizeWidget buildTransparentToOpaque({
    required ScrollController scrollController,
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
    Color? backgroundColor,
    Color? transparentColor,
    double scrollThreshold = 50.0,
  }) {
    return AnimatedAppBar(
      config: AnimatedAppBarConfig.transparentToOpaque(
        title: title,
        titleWidget: titleWidget,
        actions: actions,
        leading: leading,
        backgroundColor: backgroundColor,
        transparentColor: transparentColor,
      ).copyWith(scrollThreshold: scrollThreshold),
      scrollController: scrollController,
    );
  }

  /// 构建自定义动画AppBar
  static PreferredSizeWidget buildCustom({
    required AnimatedAppBarConfig config,
    ScrollController? scrollController,
    double? scrollOffset,
  }) {
    return AnimatedAppBar(
      config: config,
      scrollController: scrollController,
      scrollOffset: scrollOffset,
    );
  }
}

/// 扩展方法，为AnimatedAppBarConfig添加copyWith方法
extension AnimatedAppBarConfigExtension on AnimatedAppBarConfig {
  AnimatedAppBarConfig copyWith({
    String? title,
    Widget? titleWidget,
    List<Widget>? actions,
    Widget? leading,
    Color? backgroundColor,
    Color? transparentColor,
    double? elevation,
    double? transparentElevation,
    bool? centerTitle,
    double? toolbarHeight,
    TextStyle? titleTextStyle,
    TextStyle? transparentTitleTextStyle,
    IconThemeData? iconTheme,
    IconThemeData? transparentIconTheme,
    IconThemeData? actionsIconTheme,
    IconThemeData? transparentActionsIconTheme,
    double? scrollThreshold,
    Curve? animationCurve,
    Duration? animationDuration,
  }) {
    return AnimatedAppBarConfig(
      title: title ?? this.title,
      titleWidget: titleWidget ?? this.titleWidget,
      actions: actions ?? this.actions,
      leading: leading ?? this.leading,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      transparentColor: transparentColor ?? this.transparentColor,
      elevation: elevation ?? this.elevation,
      transparentElevation: transparentElevation ?? this.transparentElevation,
      centerTitle: centerTitle ?? this.centerTitle,
      toolbarHeight: toolbarHeight ?? this.toolbarHeight,
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      transparentTitleTextStyle: transparentTitleTextStyle ?? this.transparentTitleTextStyle,
      iconTheme: iconTheme ?? this.iconTheme,
      transparentIconTheme: transparentIconTheme ?? this.transparentIconTheme,
      actionsIconTheme: actionsIconTheme ?? this.actionsIconTheme,
      transparentActionsIconTheme: transparentActionsIconTheme ?? this.transparentActionsIconTheme,
      scrollThreshold: scrollThreshold ?? this.scrollThreshold,
      animationCurve: animationCurve ?? this.animationCurve,
      animationDuration: animationDuration ?? this.animationDuration,
    );
  }
} 