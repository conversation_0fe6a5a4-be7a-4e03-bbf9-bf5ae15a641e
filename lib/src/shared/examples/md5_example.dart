import 'dart:typed_data';
import '../utils/md5_utils.dart';

/// Example usage of MD5Utils class
class MD5Example {
  static void runExamples() {
    print('=== MD5Utils Examples ===\n');

    // Basic string hashing
    print('1. Basic string hashing:');
    String text = 'Hello, World!';
    String hash = MD5Utils.hexMD5(text);
    print('Input: "$text"');
    print('MD5 Hash: $hash\n');

    // Empty string
    print('2. Empty string:');
    String emptyHash = MD5Utils.hexMD5('');
    print('Input: ""');
    print('MD5 Hash: $emptyHash\n');

    // Base64 output
    print('3. Base64 output:');
    String base64Hash = MD5Utils.b64MD5(text);
    print('Input: "$text"');
    print('MD5 Base64: $base64Hash\n');

    // UTF-8 string
    print('4. UTF-8 string:');
    String utf8Text = 'Hello 世界 🌍';
    String utf8Hash = MD5Utils.hexMD5FromUtf8(utf8Text);
    print('Input: "$utf8Text"');
    print('MD5 Hash (UTF-8): $utf8Hash\n');

    // Bytes input
    print('5. Bytes input:');
    Uint8List bytes = Uint8List.fromList([72, 101, 108, 108, 111]); // "Hello"
    String bytesHash = MD5Utils.hexMD5FromBytes(bytes);
    print('Input bytes: $bytes');
    print('MD5 Hash: $bytesHash\n');

    // Compare with calcMD5 (alias)
    print('6. Using calcMD5 alias:');
    String aliasHash = MD5Utils.calcMD5(text);
    print('Input: "$text"');
    print('calcMD5 result: $aliasHash');
    print('Same as hexMD5: ${hash == aliasHash}\n');

    // Long string
    print('7. Long string:');
    String longText = 'This is a very long string that will be used to test the MD5 algorithm with longer inputs to ensure it works correctly with multiple blocks of data.';
    String longHash = MD5Utils.hexMD5(longText);
    print('Input length: ${longText.length} characters');
    print('MD5 Hash: $longHash\n');

    // Common use cases
    print('8. Common use cases:');
    
    // Password hashing (note: MD5 is not secure for passwords in production)
    String password = 'mySecretPassword123';
    String passwordHash = MD5Utils.hexMD5(password);
    print('Password hash: $passwordHash');
    
    // File checksum simulation
    String fileContent = 'This simulates file content for checksum calculation';
    String checksumHash = MD5Utils.hexMD5(fileContent);
    print('File checksum: $checksumHash');
    
    // Data integrity check
    String data = 'Important data that needs integrity verification';
    String integrityHash = MD5Utils.hexMD5(data);
    print('Data integrity hash: $integrityHash\n');

    print('=== Examples completed ===');
  }

  /// Utility method to verify data integrity
  static bool verifyDataIntegrity(String data, String expectedHash) {
    String actualHash = MD5Utils.hexMD5(data);
    return actualHash == expectedHash;
  }

  /// Utility method to generate a simple checksum for a list of strings
  static String generateChecksumForList(List<String> items) {
    String combined = items.join('|');
    return MD5Utils.hexMD5(combined);
  }

  /// Utility method to create a hash-based identifier
  static String createHashId(String prefix, String data) {
    String hash = MD5Utils.hexMD5(data);
    return '${prefix}_${hash.substring(0, 8)}'; // Use first 8 characters
  }
}
