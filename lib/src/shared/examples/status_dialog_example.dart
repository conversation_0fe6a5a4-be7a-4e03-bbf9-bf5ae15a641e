import 'package:flutter/material.dart';
import 'package:flutter_kit/src/shared/components/dialogs/status_dialog.dart';

/// StatusDialog 使用示例
/// 展示如何在不同场景下使用状态弹窗
class StatusDialogExample extends StatefulWidget {
  const StatusDialogExample({super.key});

  @override
  State<StatusDialogExample> createState() => _StatusDialogExampleState();
}

class _StatusDialogExampleState extends State<StatusDialogExample> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('StatusDialog 示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '状态弹窗示例',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // 加载中弹窗示例
            ElevatedButton(
              onPressed: _showLoadingExample,
              child: const Text('显示加载中弹窗'),
            ),
            const SizedBox(height: 10),
            
            // 加载中带超时的弹窗示例
            ElevatedButton(
              onPressed: _showLoadingWithTimeoutExample,
              child: const Text('显示加载中弹窗（5秒超时）'),
            ),
            const SizedBox(height: 10),
            
            // 成功弹窗示例
            ElevatedButton(
              onPressed: _showSuccessExample,
              child: const Text('显示成功弹窗'),
            ),
            const SizedBox(height: 10),
            
            // 错误弹窗示例
            ElevatedButton(
              onPressed: _showErrorExample,
              child: const Text('显示错误弹窗'),
            ),
            const SizedBox(height: 10),
            
            // 超时弹窗示例
            ElevatedButton(
              onPressed: _showTimeoutExample,
              child: const Text('显示超时弹窗'),
            ),
            const SizedBox(height: 10),
            
            // 模拟网络请求示例
            ElevatedButton(
              onPressed: _simulateNetworkRequest,
              child: const Text('模拟网络请求（随机成功/失败）'),
            ),
            const SizedBox(height: 10),
            
            // 手动隐藏弹窗
            ElevatedButton(
              onPressed: _hideDialog,
              child: const Text('手动隐藏弹窗'),
            ),
            const SizedBox(height: 20),
            
            const Text(
              '使用说明：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const Text(
              '• 加载中弹窗：用于显示正在处理的操作\n'
              '• 成功弹窗：操作成功后显示，2秒后自动隐藏\n'
              '• 错误弹窗：操作失败后显示，3秒后自动隐藏\n'
              '• 超时弹窗：请求超时后显示，3秒后自动隐藏\n'
              '• 弹窗显示时会阻止用户点击页面其他元素',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示基本加载中弹窗
  void _showLoadingExample() {
    StatusDialog.showLoading(
      context: context,
      message: '数据加载中...',
    );
    
    // 3秒后自动隐藏
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        StatusDialog.hide(context: context);
      }
    });
  }

  /// 显示带超时的加载中弹窗
  void _showLoadingWithTimeoutExample() {
    StatusDialog.showLoading(
      context: context,
      message: '请求处理中...',
      timeoutDuration: const Duration(seconds: 5),
      onTimeout: () {
        print('请求超时了！');
        // 超时后会自动显示超时弹窗
      },
    );
  }

  /// 显示成功弹窗
  void _showSuccessExample() {
    StatusDialog.showSuccess(
      context: context,
      message: '操作成功完成！',
      onAutoHide: () {
        print('成功弹窗自动隐藏了');
      },
    );
  }

  /// 显示错误弹窗
  void _showErrorExample() {
    StatusDialog.showError(
      context: context,
      message: '操作失败，请重试',
      onAutoHide: () {
        print('错误弹窗自动隐藏了');
      },
    );
  }

  /// 显示超时弹窗
  void _showTimeoutExample() {
    StatusDialog.showTimeout(
      context: context,
      message: '请求超时，请检查网络',
      onAutoHide: () {
        print('超时弹窗自动隐藏了');
      },
    );
  }

  /// 模拟网络请求
  void _simulateNetworkRequest() async {
    // 显示加载中
    StatusDialog.showLoading(
      context: context,
      message: '正在提交数据...',
      timeoutDuration: const Duration(seconds: 10),
      onTimeout: () {
        print('网络请求超时');
      },
    );

    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 2));

    // 隐藏加载弹窗
    if (mounted) {
      StatusDialog.hide(context: context);
    }

    // 随机显示成功或失败
    final isSuccess = DateTime.now().millisecondsSinceEpoch % 2 == 0;
    
    if (mounted) {
      if (isSuccess) {
        StatusDialog.showSuccess(
          context: context,
          message: '数据提交成功！',
          onAutoHide: () {
            print('提交成功，可以执行后续操作');
          },
        );
      } else {
        StatusDialog.showError(
          context: context,
          message: '数据提交失败，请重试',
          onAutoHide: () {
            print('提交失败，用户可以重新尝试');
          },
        );
      }
    }
  }

  /// 手动隐藏弹窗
  void _hideDialog() {
    StatusDialog.hide(context: context);
  }
}

/// 在其他页面中使用StatusDialog的示例方法
class StatusDialogUsageExamples {
  
  /// 登录示例
  static Future<void> loginExample(BuildContext context) async {
    // 显示登录加载
    StatusDialog.showLoading(
      context: context,
      message: '登录中...',
      timeoutDuration: const Duration(seconds: 30),
    );

    try {
      // 模拟登录API调用
      await Future.delayed(const Duration(seconds: 2));
      
      // 隐藏加载弹窗
      StatusDialog.hide(context: context);
      
      // 显示登录成功
      StatusDialog.showSuccess(
        context: context,
        message: '登录成功',
        onAutoHide: () {
          // 跳转到主页
          print('跳转到主页');
        },
      );
    } catch (e) {
      // 隐藏加载弹窗
      StatusDialog.hide(context: context);
      
      // 显示登录失败
      StatusDialog.showError(
        context: context,
        message: '登录失败，请检查账号密码',
      );
    }
  }

  /// 数据保存示例
  static Future<void> saveDataExample(BuildContext context) async {
    StatusDialog.showLoading(
      context: context,
      message: '保存中...',
    );

    try {
      // 模拟保存操作
      await Future.delayed(const Duration(seconds: 1));
      
      StatusDialog.hide(context: context);
      StatusDialog.showSuccess(
        context: context,
        message: '保存成功',
      );
    } catch (e) {
      StatusDialog.hide(context: context);
      StatusDialog.showError(
        context: context,
        message: '保存失败',
      );
    }
  }

  /// 文件上传示例
  static Future<void> uploadFileExample(BuildContext context) async {
    StatusDialog.showLoading(
      context: context,
      message: '文件上传中...',
      timeoutDuration: const Duration(minutes: 5), // 文件上传可能需要更长时间
      onTimeout: () {
        print('文件上传超时');
      },
    );

    // 模拟文件上传过程...
  }
}
