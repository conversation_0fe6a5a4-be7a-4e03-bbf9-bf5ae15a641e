import 'package:flutter_kit/src/features/login/logic/examinee_helper.dart';
import 'package:flutter_kit/src/shared/locator.dart';

/// ExamineeHelper使用示例
class ExamineeHelperExample {
  static Future<void> runExamples() async {
    print('=== ExamineeHelper Examples ===\n');

    final ExamineeHelper helper = locator<ExamineeHelper>();

    // 1. 保存token
    print('1. 保存考生token:');
    String sampleToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.sample.token';
    bool tokenSaved = await helper.examinee_token(sampleToken);
    print('Token保存结果: $tokenSaved');
    print('保存的Token: $sampleToken\n');

    // 2. 获取token
    print('2. 获取考生token:');
    String? retrievedToken = await helper.getExamineeToken();
    print('获取的Token: $retrievedToken\n');

    // 3. 检查登录状态
    print('3. 检查登录状态:');
    bool loginStatus = await helper.isLogin();
    print('登录状态: $loginStatus\n');

    // 4. 保存用户信息
    print('4. 保存用户信息:');
    Map<String, dynamic> userInfo = {
      'id': '12345',
      'name': '张三',
      'email': '<EMAIL>',
      'phone': '13800138000',
      'avatar': 'https://example.com/avatar.jpg',
      'createTime': DateTime.now().toIso8601String(),
    };
    bool userInfoSaved = await helper.saveUserInfo(userInfo);
    print('用户信息保存结果: $userInfoSaved');
    print('保存的用户信息: $userInfo\n');

    // 5. 获取用户信息
    print('5. 获取用户信息:');
    Map<String, dynamic>? retrievedUserInfo = await helper.getUserInfo();
    print('获取的用户信息: $retrievedUserInfo\n');

    // 6. 完整登录流程
    print('6. 完整登录流程:');
    String newToken = 'new.jwt.token.example';
    Map<String, dynamic> newUserInfo = {
      'id': '67890',
      'name': '李四',
      'email': '<EMAIL>',
      'phone': '13900139000',
    };
    bool loginResult = await helper.login(newToken, userInfo: newUserInfo);
    print('登录结果: $loginResult');
    
    // 验证登录状态
    bool isLoggedIn = await helper.isLogin();
    print('登录后状态: $isLoggedIn\n');

    // 7. 刷新用户信息
    print('7. 刷新用户信息:');
    Map<String, dynamic> updatedUserInfo = {
      'id': '67890',
      'name': '李四',
      'email': '<EMAIL>',
      'phone': '13900139000',
      'avatar': 'https://example.com/new_avatar.jpg',
      'lastLoginTime': DateTime.now().toIso8601String(),
    };
    bool refreshResult = await helper.refreshUserInfo(updatedUserInfo);
    print('刷新用户信息结果: $refreshResult');
    
    // 获取更新后的用户信息
    Map<String, dynamic>? refreshedInfo = await helper.getUserInfo();
    print('更新后的用户信息: $refreshedInfo\n');

    // 8. 退出登录
    print('8. 退出登录:');
    bool logoutResult = await helper.logout();
    print('退出登录结果: $logoutResult');
    
    // 验证退出后状态
    bool isLoggedOut = await helper.isLogin();
    print('退出后登录状态: $isLoggedOut');
    
    // 验证数据是否清除
    String? tokenAfterLogout = await helper.getExamineeToken();
    Map<String, dynamic>? userInfoAfterLogout = await helper.getUserInfo();
    print('退出后Token: $tokenAfterLogout');
    print('退出后用户信息: $userInfoAfterLogout\n');

    print('=== Examples completed ===');
  }

  /// 模拟登录API调用后的处理
  static Future<void> simulateLoginProcess() async {
    print('=== 模拟登录流程 ===\n');

    final ExamineeHelper helper = locator<ExamineeHelper>();

    // 模拟API返回的登录数据
    Map<String, dynamic> loginResponse = {
      'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.simulated.login.token',
      'user': {
        'id': '100001',
        'username': 'examinee001',
        'name': '考生001',
        'email': '<EMAIL>',
        'phone': '13800138001',
        'status': 'active',
        'role': 'examinee',
      }
    };

    print('1. 模拟API登录响应:');
    print('Token: ${loginResponse['token']}');
    print('用户信息: ${loginResponse['user']}\n');

    // 保存登录信息
    print('2. 保存登录信息:');
    bool saveResult = await helper.login(
      loginResponse['token'] as String,
      userInfo: loginResponse['user'] as Map<String, dynamic>,
    );
    print('保存结果: $saveResult\n');

    // 验证保存结果
    print('3. 验证保存结果:');
    bool isLoggedIn = await helper.isLogin();
    String? savedToken = await helper.getExamineeToken();
    Map<String, dynamic>? savedUserInfo = await helper.getUserInfo();
    
    print('登录状态: $isLoggedIn');
    print('保存的Token: $savedToken');
    print('保存的用户信息: $savedUserInfo\n');

    print('=== 登录流程完成 ===');
  }

  /// 检查登录状态的工具方法
  static Future<bool> checkLoginStatus() async {
    final ExamineeHelper helper = locator<ExamineeHelper>();
    return await helper.isLogin();
  }

  /// 获取当前用户信息的工具方法
  static Future<Map<String, dynamic>?> getCurrentUser() async {
    final ExamineeHelper helper = locator<ExamineeHelper>();
    return await helper.getUserInfo();
  }

  /// 安全退出登录的工具方法
  static Future<bool> safeLogout() async {
    final ExamineeHelper helper = locator<ExamineeHelper>();
    try {
      return await helper.logout();
    } catch (e) {
      print('退出登录时发生错误: $e');
      // 强制清除所有数据
      await helper.clearAllData();
      return true;
    }
  }
}
