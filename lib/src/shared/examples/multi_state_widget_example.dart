import 'package:flutter/material.dart';
import '../../base/widgets/multi_state_widget.dart';
import '../../base/logic/view_state_logic.dart';
import '../components/multi_state/multi_state_view.dart';

/// MultiStateWidget 使用示例
/// 展示如何使用新的基类来简化状态管理

/// 使用方式1: 全页面状态管理（优化版）
///
/// class MyPage extends MultiStateWidget<MyLogic> {
///   @override
///   MyLogic createController() => MyLogic();
///
///   @override
///   Widget buildBody(BuildContext context, MyLogic logic) {
///     // 智能状态映射，避免初始加载时显示空数据
///     MultiViewState currentState;
///     if (logic.viewState.isLoading()) {
///       currentState = MultiViewState.loading;
///     } else if (logic.viewState.isError()) {
///       currentState = MultiViewState.error;
///     } else if (logic.viewState.isSuccess() && logic.hasData) {
///       currentState = MultiViewState.success;
///     } else if (logic.viewState.isSuccess() && !logic.hasData) {
///       currentState = MultiViewState.empty;
///     } else {
///       currentState = MultiViewState.loading; // 默认显示加载状态
///     }
///
///     final stateController = MultiStateController(currentState);
///
///     return MultiStateView(
///       controller: stateController,
///       contentWidget: MyContentWidget(data: logic.data),
///       loadingWidget: MyLoadingWidget(),
///       errorWidget: MyErrorWidget(),
///       onRetry: () => logic.loadData(),
///     );
///   }
/// }

/// 使用方式2: 部分区域状态管理
///
/// class MyPage extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return Scaffold(
///       appBar: AppBar(title: Text('我的页面')),
///       body: Column(
///         children: [
///           FixedHeaderWidget(), // 固定区域
///           Expanded(
///             child: MultiStateView( // 只对这部分进行状态管理
///               controller: myController,
///               contentWidget: MyContentList(),
///               loadingWidget: MyLoadingSkeleton(),
///             ),
///           ),
///           FixedFooterWidget(), // 固定区域
///         ],
///       ),
///     );
///   }
/// }



/// 🎯 状态管理优化重点：
///
/// 1. **避免闪现空数据界面**：
///    - 初始状态优先显示 loading，而不是 empty
///    - 只有在明确加载完成且无数据时才显示 empty
///
/// 2. **智能状态映射顺序**：
///    - loading > error/network > success(有数据) > empty(无数据)
///    - 默认状态始终是 loading，确保良好的用户体验
///
/// 3. **状态判断逻辑**：
///    ```dart
///    if (logic.viewState.isLoading()) {
///      currentState = MultiViewState.loading;
///    } else if (logic.viewState.isError()) {
///      currentState = MultiViewState.error;
///    } else if (logic.viewState.isSuccess() && hasData) {
///      currentState = MultiViewState.success;
///    } else if (logic.viewState.isSuccess() && !hasData) {
///      currentState = MultiViewState.empty; // 只有确认无数据才显示
///    } else {
///      currentState = MultiViewState.loading; // 默认加载状态
///    }
///    ```
///
/// 4. **使用建议**：
///    - 继承 MultiStateWidget<YourLogic>
///    - 实现智能状态映射逻辑
///    - 自定义各种状态的UI
///    - 合理使用 hasData 判断
