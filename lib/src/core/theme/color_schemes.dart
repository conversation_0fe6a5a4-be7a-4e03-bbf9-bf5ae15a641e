import 'package:flutter/material.dart';

const lightColorScheme = ColorScheme(
  brightness: Brightness.light,
  primary: Color(0xFFED536D), // 🔥 使用新的主色调
  onPrimary: Color(0xFFFFFFFF),
  primaryContainer: Color(0xFFFFE8EC), // 🔥 主色调的浅色容器
  onPrimaryContainer: Color(0xFF5D1B2A), // 🔥 主色调的深色文字
  secondary: Color(0xFF515A6E), // 🔥 使用textSecondary作为次要色
  onSecondary: Color(0xFFFFFFFF),
  secondaryContainer: Color(0xFFE8DEF8),
  onSecondaryContainer: Color(0xFF1D192B),
  tertiary: Color(0xFF7D5260),
  onTertiary: Color(0xFFFFFFFF),
  tertiaryContainer: Color(0xFFFFD8E4),
  onTertiaryContainer: Color(0xFF31111D),
  error: Color(0xFFED536D), // 🔥 错误色也使用主色调
  onError: Color(0xFFFFFFFF),
  errorContainer: Color(0xFFFFE8EC),
  onErrorContainer: Color(0xFF5D1B2A),
  outline: Color(0xFFF4F4F4), // 🔥 使用borderColor
  surface: Color(0xFFFFFFFF), // 🔥 使用surfaceColor
  onSurface: Color(0xFF1F2329), // 🔥 使用textPrimary
  onSurfaceVariant: Color(0xFF515A6E), // 🔥 使用textSecondary
  inverseSurface: Color(0xFF313033),
  onInverseSurface: Color(0xFFF4EFF4),
  inversePrimary: Color(0xFFFFB3C1), // 🔥 主色调的反色
  shadow: Color(0xFF000000),
  surfaceTint: Color(0xFFED536D), // 🔥 使用新的主色调
  outlineVariant: Color(0xFFF4F4F4), // 🔥 使用borderColor
  scrim: Color(0xFF000000),
);

const darkColorScheme = ColorScheme(
  brightness: Brightness.dark,
  primary: Color(0xFFD0BCFF),
  onPrimary: Color(0xFF381E72),
  primaryContainer: Color(0xFF4F378B),
  onPrimaryContainer: Color(0xFFEADDFF),
  secondary: Color(0xFFCCC2DC),
  onSecondary: Color(0xFF332D41),
  secondaryContainer: Color(0xFF4A4458),
  onSecondaryContainer: Color(0xFFE8DEF8),
  tertiary: Color(0xFFEFB8C8),
  onTertiary: Color(0xFF492532),
  tertiaryContainer: Color(0xFF633B48),
  onTertiaryContainer: Color(0xFFFFD8E4),
  error: Color(0xFFF2B8B5),
  onError: Color(0xFF601410),
  errorContainer: Color(0xFF8C1D18),
  onErrorContainer: Color(0xFFF9DEDC),
  outline: Color(0xFF938F99),
  surface: Color.fromARGB(255, 37, 37, 41),
  onSurface: Color.fromARGB(255, 226, 227, 216),
  onSurfaceVariant: Color(0xFFCAC4D0),
  inverseSurface: Color(0xFFE6E1E5),
  onInverseSurface: Color(0xFF313033),
  inversePrimary: Color(0xFF6750A4),
  shadow: Color(0xFF000000),
  surfaceTint: Color(0xFFD0BCFF),
  outlineVariant: Color(0xFF49454F),
  scrim: Color(0xFF000000),
);
