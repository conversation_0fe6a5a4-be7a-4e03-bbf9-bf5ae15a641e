import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../features/discover/ui/discover_screen.dart';
import '../../features/enterpriseDetail/ui/enterprise_detail_screen.dart';
import '../../features/home/<USER>/home_screen_new.dart';
import '../../features/jobDetail/ui/job_detail_screen.dart';
import '../../features/login/login_screen.dart';
import '../../features/main_tab/ui/main_tab_screen.dart';
import '../../features/messages/ui/messages_screen.dart';
import '../../features/profile/ui/profile_screen.dart';
import '../../features/resume/ui/resume_screen.dart';
import '../../features/resume_library/ui/resume_library_screen.dart';
import '../../features/splash/ui/splash_screen.dart';

part 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen,Route')
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> routes = [
    AutoRoute(page: SplashRoute.page, initial: true),
    AutoRoute(page: LoginRoute.page),
    AutoRoute(page: MainTabRoute.page),
    AutoRoute(page: HomeRouteNew.page),
    AutoRoute(page: DiscoverRoute.page),
    AutoRoute(page: MessagesRoute.page),
    AutoRoute(page: ProfileRoute.page),
    AutoRoute(page: JobDetailRoute.page),
    AutoRoute(page: EnterpriseDetailRoute.page),
    AutoRoute(page: ResumeRoute.page),
    AutoRoute(page: ResumeLibraryRoute.page)
  ];




}
