import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_color.dart';
import '../../../core/routing/app_router.dart';
import '../../../base/widgets/view_state_widget.dart';
import '../../../shared/locator.dart';
import '../logic/splash_logic.dart';

@RoutePage()
class SplashScreen extends ViewStateWidget<SplashLogic> {
  const SplashScreen({super.key});

  @override
  SplashLogic createController() {
    return locator<SplashLogic>();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, SplashLogic logic) {
    return null; // 启动页不需要AppBar
  }

  @override
  Widget buildBody(BuildContext context, SplashLogic logic) {
    // 设置导航回调
    logic.onNavigateToMain ??= () {
      context.router.replace(const MainTabRoute());
    };

    return Scaffold(
      backgroundColor: AppColors.white,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: AppColors.white,
        child: SafeArea(
          child: Column(
            children: [
              // 中间弹性空间
              const Spacer(),

              // Logo 区域
              _buildLogoSection(),

              // 中间弹性空间
              const Spacer(),

              // 底部标语
              _buildBottomSlogan(),

              // 底部空间
              SizedBox(height: 60.h),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建 Logo 区域
  Widget _buildLogoSection() {
    return Center(
      child: Image.asset(
        'assets/images/zd_logo.png',
        width: 200.w,
        height: 200.w,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          // 如果图片加载失败，显示默认图标
          return Container(
            width: 694.w,
            height: 259.w,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20.w),
            ),
            child: Icon(
              Icons.work_outline,
              size: 100.w,
              color: AppColors.primary,
            ),
          );
        },
      ),
    );
  }

  /// 构建底部标语
  Widget _buildBottomSlogan() {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
          height: 1.2,
        ),
        children: [
          TextSpan(
            text: '职',
            style: TextStyle(color: AppColors.primary),
          ),
          TextSpan(
            text: '道有',
            style: TextStyle(color: AppColors.textPrimary),
          ),
          TextSpan(
            text: '得',
            style: TextStyle(color: AppColors.primary),
          ),
          TextSpan(
            text: ' 招聘无忧',
            style: TextStyle(color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }
}
