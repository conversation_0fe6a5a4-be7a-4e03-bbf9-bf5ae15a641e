import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/features/home/<USER>/job_list_item.dart';
import 'package:flutter_kit/src/shared/components/forms/input.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../datasource/repositories/home_repository.dart';
import '../logic/home_controller.dart';

@RoutePage()
class HomeScreen extends StatefulWidget implements AutoRouteWrapper {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) =>
          HomeController(repository: HomeReportsitory())..fetchJobs(),
      child: this,
    );
  }
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  final List<Map<String, String>> _tabs = [
    {'text': "全职", 'icon': 'ic_ygz'},
    {'text': "兼职", 'icon': 'ic_ygz'},
    {'text': "普工", 'icon': 'ic_ygz'},
    {'text': "销售", 'icon': 'ic_ygz'},
    {'text': "招聘会", 'icon': 'ic_ygz'}
  ];
  late final TextEditingController _searchController;

  @override
  void initState() {
    _searchController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildTabs() {
    return Container(
      margin: EdgeInsets.fromLTRB(12.w, 0, 12.w, 0),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.w),
          child: Container(
            height: 80.h,
            color: Colors.white,
            child: Row(
                children: _tabs
                    .map((tab) => Expanded(
                            child: Container(
                          margin: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 8.w),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                  height: 30.w,
                                  width: 30.w,
                                  child: Image.asset(
                                    'assets/images/${tab['icon']}.png',
                                    fit: BoxFit.cover,
                                  )),
                              SizedBox(
                                height: 6.w,
                              ),
                              Text(
                                tab['text']!,
                                style: TextStyle(
                                    fontSize: 13.w,
                                    color: AppColors.color_333333,
                                    fontWeight: FontWeight.w600),
                              )
                            ],
                          ),
                        )))
                    .toList()),
          )),
    );
  }

  Widget _buildBanner() {
    return SizedBox(
      height: 160.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.w),
        child: PageView(
          children: [
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            ),
            Image.asset(
              'assets/images/bg_gongsi.jpg',
              fit: BoxFit.cover,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildJobExpection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.w),
      child: Container(
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 12.w),
          child: Row(
            children: [
              Text(
                '行政专员',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.w),
              ),
              Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  //暂不考虑
                  Container(
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(14.w),
                          child: Container(
                            color: AppColors.color_ff0027,
                            child: Row(
                              children: [
                                SizedBox(
                                    width: 22.w,
                                    height: 22.w,
                                    child: Image.asset(
                                      'assets/images/ic_zaizhi_yuenei.png',
                                      fit: BoxFit.cover,
                                    )),
                                SizedBox(width: 8.w),
                                Text(
                                  '暂不考虑',
                                  style: TextStyle(
                                      fontSize: 11.w,
                                      color: AppColors.color_666666),
                                ),
                                SizedBox(
                                  width: 8.w,
                                )
                              ],
                            ),
                          ))),
                  SizedBox(width: 10.w),
                  //右图标
                  SizedBox(
                    width: 5.w,
                    child: Image.asset('assets/images/ic_arrow_see.png',
                        fit: BoxFit.cover),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );

  }

  Widget _buildTitleBar() {
    return Container(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 12.w),
      child: Row(
        children: [
          Row(
            children: [
              Icon(Icons.location_on,size: 14.w,color: AppColors.color_333333),
              SizedBox(width: 2.w),
              Text(
                '汕头',
                style: TextStyle(
                  fontSize: 13.w,
                  fontWeight: FontWeight.bold,
                  color: AppColors.color_333333,
                ),
              ),
              SizedBox(width: 0.w),
              Icon(Icons.arrow_drop_down, size: 20.w, color: AppColors.color_333333),
            ],
          ),
          SizedBox(width: 6.w),
          Expanded(
            child: Card(
              color: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.w)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w,vertical: 8.w),
                child: Row(
                  children: [
                    Icon(Icons.search,size: 20.w,color: AppColors.color_333333),
                    SizedBox(width: 6,),
                    Expanded(
                      child: Text('搜索职位名称',style: TextStyle(fontSize: 15.w,color: AppColors.color_cccccc),)
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
    children: [
      //背景图片
      Column(
        children: [
          Image.asset(
            'assets/images/bg_index.png',
            width: double.infinity,
            height: 240.w,
            fit: BoxFit.cover, // 宽度铺满，高度自适应填满
          ),
          Expanded(child: Container(color: AppColors.color_background))
        ],
      ),


      //页面内容
      Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(title: _buildTitleBar(),backgroundColor: Colors.transparent,elevation: 0,),
        body: Container(
          child: Consumer<HomeController>(builder: (context, controller, _) {
            final state = controller.value;
            return ListView(
              padding: EdgeInsets.zero,
              children: [
                Padding(
                    padding: EdgeInsets.fromLTRB(12.w, 16.w, 12.w, 16.w),
                    child: _buildBanner()),
                _buildTabs(),
                Container(
                    margin: EdgeInsets.fromLTRB(12.w, 12.w, 12.w, 6.w),
                    child: _buildJobExpection()),
                if (state.isLoading)
                  const Center(child: CircularProgressIndicator())
                else if (state.error != null)
                  Center(
                    child: Text('Error:${state.error}'),
                  )
                else if (state.jobs.isEmpty)
                    const Center(child: Text('没有找到岗位信息'))
                  else
                    ...List.generate(state.jobs.length,
                            (i) => JobListItem(item: state.jobs[i], index: i))
              ],
            );
          }),
        )
      )
    ],
    );
  }
}
