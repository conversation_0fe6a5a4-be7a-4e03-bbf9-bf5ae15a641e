


import 'package:flutter/cupertino.dart';

import '../../../datasource/repositories/home_repository.dart';
import 'home_state.dart';

class HomeController extends ValueNotifier<HomeState> {
  final HomeReportsitory repository;

  HomeController({required this.repository}) : super(HomeState());

  Future<void> fetchJobs() async {
    value = value.copyWith(isLoading: true, error: null);
    final response = await repository.getHomeList(1,20);
    response.when(
      success: (data, message) {
        value = value.copyWith(
          isLoading: false,
          jobs: data,
        );
      },
      failure: (message, code, error) {
        value = value.copyWith(
          isLoading: false,
          error: message,
        );
      },
      empty: (message) {
        value = value.copyWith(
          isLoading: false,
          jobs: [],
        );
      },
    );
  }
}