import '../../../datasource/models/job_info_entity.dart';

class HomeState {
  final bool isLoading;
  final List<JobInfoEntity> jobs;
  final String? error;

  HomeState({
    this.isLoading = false,
    this.jobs = const [],
    this.error,
  });

  HomeState copyWith({
    bool? isLoading,
    List<JobInfoEntity>? jobs,
    String? error,
  }) {
    return HomeState(
      isLoading: isLoading ?? this.isLoading,
      jobs: jobs ?? this.jobs,
      error: error,
    );
  }
}