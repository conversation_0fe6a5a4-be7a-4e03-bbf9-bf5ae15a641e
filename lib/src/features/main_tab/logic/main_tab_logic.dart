import 'package:flutter_kit/src/base/logic/view_state_logic.dart';

/// 主Tab页面逻辑
class MainTabLogic extends ViewStateLogic {
  int _currentIndex = 0;
  
  int get currentIndex => _currentIndex;
  
  /// 切换Tab
  void switchTab(int index) {
    if (_currentIndex != index) {
      _currentIndex = index;
      notifyListeners();
    }
  }
  
  @override
  void loadData() {
    // 主Tab页面不需要加载数据，直接设置成功状态
    setSuccess(null);
  }
}
