import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/datasource/models/enterprise_detail_entity.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../core/routing/app_router.dart';
import '../../../core/theme/app_color.dart';
import '../../../shared/components/app_bars/unified_app_bar.dart';
import '../../jobDetail/ui/job_detail_view.dart';

// --- UI 设计规范常量 ---

class AppTextStyles {
  static const String fontFamily = 'Noto Sans SC';

  static const TextStyle h1 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );

  static const TextStyle h2 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );

  static const TextStyle bh2 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.color_333333,
  );

  static const TextStyle body = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    color: AppColors.white,
    height: 1.6,
  );

  static const TextStyle tag = TextStyle(
    fontFamily: fontFamily,
    fontSize: 13,
    color: AppColors.white,
  );
  static const TextStyle point = TextStyle(
    fontFamily: fontFamily,
    fontSize: 15,
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );

  static const TextStyle smallTag = TextStyle(
    fontFamily: fontFamily,
    fontSize: 11,
    color: AppColors.white,
  );

  static const TextStyle benefitText = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    color: AppColors.white,
  );

  static const TextStyle salary = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.color_f02e4b,
  );
}

// --- SVG 占位图资源 ---
class AppSvgs {
  static const String logoPlaceholder = '''
  <svg viewBox="0 0 24 24" fill="none" stroke="#9CA3AF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
      <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
  </svg>
  ''';

  static const String banner1 = '''
  <svg viewBox="0 0 200 120" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="120" fill="#374151"/>
      <rect x="40" y="50" width="120" height="40" rx="2" fill="#4B5563"/>
      <rect x="45" y="55" width="50" height="30" rx="1" fill="#6B7280"/>
      <rect x="105" y="55" width="50" height="30" rx="1" fill="#6B7280"/>
      <line x1="100" y1="50" x2="100" y2="90" stroke="#9CA3AF" stroke-width="2"/>
      <circle cx="160" cy="40" r="10" fill="#10B981"/>
      <path d="M160 30 V 20 M 155 25 h 10" stroke="#D1D5DB" stroke-width="2" stroke-linecap="round"/>
      <text x="100" y="110" font-family="Noto Sans SC, sans-serif" font-size="10" fill="#9CA3AF" text-anchor="middle">办公环境</text>
  </svg>
  ''';

  static const String banner2 = '''
  <svg viewBox="0 0 200 120" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="120" fill="#374151"/>
      <circle cx="70" cy="50" r="10" fill="#60A5FA"/>
      <path d="M70 60 C 60 80, 80 80, 70 60 Z" fill="#60A5FA"/>
      <circle cx="100" cy="45" r="12" fill="#3B82F6"/>
      <path d="M100 57 C 88 82, 112 82, 100 57 Z" fill="#3B82F6"/>
      <circle cx="130" cy="50" r="10" fill="#60A5FA"/>
      <path d="M130 60 C 120 80, 140 80, 130 60 Z" fill="#60A5FA"/>
      <text x="100" y="110" font-family="Noto Sans SC, sans-serif" font-size="10" fill="#9CA3AF" text-anchor="middle">团队活动</text>
  </svg>
  ''';

  static const String mapPlaceholder = '''
  <svg viewBox="0 0 200 107" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="107" fill="#374151"/>
      <path d="M20 87 L 60 77 L 100 97 L 140 77 L 180 87" stroke="#4B5563" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M40 20 L 80 40 L 120 20 L 160 40" stroke="#4B5563" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M100 40 C 90 40, 90 55, 100 55 C 110 55, 110 40, 100 40 Z" fill="#F02E4B"/>
      <circle cx="100" cy="44" r="2" fill="white"/>
      <text x="100" y="100" font-family="Noto Sans SC, sans-serif" font-size="8" fill="#9CA3AF" text-anchor="middle">地图预览</text>
  </svg>
  ''';
}

class MainContent extends StatefulWidget {
  final EnterpriseDetailEntity enterpriseDetailEntity;

  const MainContent({required this.enterpriseDetailEntity});

  @override
  State<MainContent> createState() => _MainContentState();
}

class _MainContentState extends State<MainContent> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ListView(
          controller: _scrollController,
          padding: EdgeInsets.fromLTRB(
              0, MediaQuery.of(context).viewPadding.top + 60.w, 0, 120.w),
          children: [
            _CompanyHeader(
              enterpriseDetailEntity: widget.enterpriseDetailEntity,
            ),
            SizedBox(height: 20),
            _Section(child: _WorkBenefitsSection()),
            _Section(child: _BannerSection()),
            _Section(
                child: _IntroductionSection(
              enterpriseDetailEntity: widget.enterpriseDetailEntity,
            )),
            _Section(
                child: _LocationSection(
              enterpriseDetailEntity: widget.enterpriseDetailEntity,
            )),
          ],
        ),
        // 添加透明滚动AppBar
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: UnifiedAppBar.scrollableTitleTransparent(
            scrollController: _scrollController,
            title: widget.enterpriseDetailEntity.name,
            onBackPressed: () {
              Navigator.of(context).pop();
            },
            backgroundColor: Color(0xFF252C44),
            contentColor: AppColors.white,
          ),
        ),
      ],
    );
  }
}

class _Section extends StatelessWidget {
  final Widget child;

  const _Section({required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      margin: const EdgeInsets.only(bottom: 20),
      decoration: const BoxDecoration(
          // border: Border(top: BorderSide(color: AppColors.white)),
          ),
      child: Padding(
        padding: const EdgeInsets.only(top: 20),
        child: child,
      ),
    );
  }
}

class _CompanyHeader extends StatelessWidget {
  final EnterpriseDetailEntity enterpriseDetailEntity;

  const _CompanyHeader({required this.enterpriseDetailEntity});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 64,
              height: 64,
              child: enterpriseDetailEntity.logo.isNotEmpty
                  ? Image.network(
                      enterpriseDetailEntity.logo,
                      width: 64,
                      height: 64,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultLogo();
                      },
                    )
                  : _buildDefaultLogo(),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(enterpriseDetailEntity.name, style: AppTextStyles.h1),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: [
                    Text(enterpriseDetailEntity.nature,
                        style: AppTextStyles.tag),
                    Text("·", style: AppTextStyles.tag),
                    Text(enterpriseDetailEntity.registeredCapital,
                        style: AppTextStyles.tag),
                    Text("·", style: AppTextStyles.tag),
                    Text(enterpriseDetailEntity.peopleNum,
                        style: AppTextStyles.tag),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建默认Logo
  Widget _buildDefaultLogo() {
    return SvgPicture.asset(
      "assets/images/company_default.svg",
      width: 64,
      height: 64,
      fit: BoxFit.cover,
    );
  }
}

class _WorkBenefitsSection extends StatelessWidget {
  const _WorkBenefitsSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text("工作福利", style: AppTextStyles.h2),
        SizedBox(height: 16.w),
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: [
            SizedBox(
                width: (MediaQuery.of(context).size.width - 32 - 48) / 4,
                child: _BenefitItem(icon: Icons.card_giftcard, text: "年终奖金")),
            SizedBox(
                width: (MediaQuery.of(context).size.width - 32 - 48) / 4,
                child: _BenefitItem(
                    icon: Icons.watch_later_outlined, text: "周末双休")),
            SizedBox(
                width: (MediaQuery.of(context).size.width - 32 - 48) / 4,
                child: _BenefitItem(
                    icon: Icons.free_breakfast_outlined, text: "下午茶")),
            SizedBox(
                width: (MediaQuery.of(context).size.width - 32 - 48) / 4,
                child: _BenefitItem(
                    icon: Icons.beach_access_outlined, text: "带薪年假")),
            SizedBox(
                width: (MediaQuery.of(context).size.width - 32 - 48) / 4,
                child: _BenefitItem(
                    icon: Icons.beach_access_outlined, text: "带薪年假")),
            SizedBox(
                width: (MediaQuery.of(context).size.width - 32 - 48) / 4,
                child: _BenefitItem(
                    icon: Icons.beach_access_outlined, text: "带薪年假")),
          ],
        )
      ],
    );
  }
}

class _BenefitItem extends StatelessWidget {
  final IconData icon;
  final String text;

  const _BenefitItem({required this.icon, required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
        color: AppColors.alpha_10_white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 28, color: AppColors.white),
          const SizedBox(height: 8),
          Text(text,
              style: AppTextStyles.benefitText, textAlign: TextAlign.center),
        ],
      ),
    );
  }
}

class _BannerSection extends StatefulWidget {
  const _BannerSection();

  @override
  State<_BannerSection> createState() => _BannerSectionState();
}

class _BannerSectionState extends State<_BannerSection> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  Timer? _timer;

  final List<String> _banners = [AppSvgs.banner1, AppSvgs.banner2];

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 3), (Timer timer) {
      if (_currentPage < _banners.length - 1) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeIn,
      );
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text("公司相册", style: AppTextStyles.h2),
        const SizedBox(height: 12),
        Stack(
          children: [
            SizedBox(
              height: 160,
              child: PageView.builder(
                controller: _pageController,
                itemCount: _banners.length,
                onPageChanged: (int page) {
                  setState(() {
                    _currentPage = page;
                  });
                },
                itemBuilder: (context, index) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: SvgPicture.string(
                      _banners[index],
                      fit: BoxFit.cover,
                    ),
                  );
                },
              ),
            ),
            Positioned(
              bottom: 10,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(_banners.length, (index) {
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    height: 6,
                    width: _currentPage == index ? 18 : 6,
                    decoration: BoxDecoration(
                      color: _currentPage == index
                          ? Colors.white
                          : Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _IntroductionSection extends StatelessWidget {
  final EnterpriseDetailEntity enterpriseDetailEntity;

  const _IntroductionSection({required this.enterpriseDetailEntity});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("企业介绍", style: AppTextStyles.h2),
        SizedBox(height: 12),
        Text(
          enterpriseDetailEntity.intro,
          style: AppTextStyles.body,
        ),
      ],
    );
  }
}

class _LocationSection extends StatelessWidget {
  final EnterpriseDetailEntity enterpriseDetailEntity;

  const _LocationSection({required this.enterpriseDetailEntity});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text("公司位置", style: AppTextStyles.h2),
        const SizedBox(height: 12),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(Icons.location_on, color: AppColors.white, size: 20),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                enterpriseDetailEntity.address,
                style: TextStyle(
                    fontSize: 16.w, color: AppColors.white, height: 1.6),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: Stack(
            alignment: Alignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Text("公司位置",style: TextStyle(color: AppColors.white),),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class JobListDrawer extends StatelessWidget {
  final ScrollController scrollController;
  final EnterpriseDetailEntity enterpriseDetailEntity;

  const JobListDrawer(
      {required this.scrollController, required this.enterpriseDetailEntity});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 5,
          margin: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.color_cccccc,
            borderRadius: BorderRadius.circular(5),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 12.0),
          child: Text("在招职位 · ${enterpriseDetailEntity.jobs.length}",
              style: AppTextStyles.bh2),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.zero,
            controller: scrollController,
            itemCount: enterpriseDetailEntity.jobs.length,
            itemBuilder: (context, index) {
              return _JobItemCard(
                  item: enterpriseDetailEntity.jobs[index], index: index);
            },
          ),
        ),
      ],
    );
  }
}

class _JobItemCard extends StatelessWidget {
  final EnterpriseDetailJobs item;
  final int index;

  const _JobItemCard({required this.item, required this.index});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // 跳转到职位详情页面
        context.router.push(JobDetailRoute(jobId: item.id));
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 8.w, horizontal: 16.w),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                  color: AppColors.alpha_10_white,
                  blurRadius: 2.w,
                  offset: Offset(0, 2))
            ]),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            children: [
              Expanded(
                  flex: 2,
                  child: Text(
                    item.name,
                    style:
                        TextStyle(fontSize: 18.w, fontWeight: FontWeight.w600),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )),
              SizedBox(
                width: 6.w,
              ),
              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  item.pay + ' ' + item.jobPayUnit,
                  style: TextStyle(
                      fontSize: 17.w,
                      color: AppColors.color_f02e4b,
                      fontWeight: FontWeight.w600),
                ),
              )
            ],
          ),
          SizedBox(height: 10.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(4.w)),
            child: Text(item.jobTypeName,
                style: TextStyle(
                    color: AppColors.color_666666,
                    fontSize: 14.w,
                    fontWeight: FontWeight.w600)),
          ),
          SizedBox(height: 10.w),
          //标签
          Wrap(
            spacing: 8.w,
            children: (['标签栏', '标签栏', '标签栏'] as List<String>).map((tag) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.w),
                decoration: BoxDecoration(
                  color: AppColors.color_f6f8fb,
                  borderRadius: BorderRadius.circular(4.w),
                ),
                child: Text(
                  tag,
                  style:
                      TextStyle(color: AppColors.color_686868, fontSize: 14.w),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 12.w),
          Row(
            children: [
              CircleAvatar(
                  backgroundImage: NetworkImage(item.enterpriseLogoSmall),
                  radius: 15.w),
              SizedBox(width: 6.w),
              Expanded(
                  child: Text(
                item.enterpriseName,
                style: TextStyle(fontSize: 12.w, color: AppColors.color_666666),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
              SizedBox(
                width: 10.w,
              ),
              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  '汕头市',
                  style:
                      TextStyle(fontSize: 12.w, color: AppColors.color_686868),
                ),
              )
            ],
          ),
        ]),
      ),
    );
  }
}
