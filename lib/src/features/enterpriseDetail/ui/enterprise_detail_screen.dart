import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/widgets/multi_state_widget.dart';
import 'package:flutter_kit/src/datasource/repositories/enterprise_detail_repository.dart';
import 'package:flutter_kit/src/features/enterpriseDetail/logic/enterprise_detail_logic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

import '../../../core/theme/app_color.dart';
import '../../../shared/locator.dart';
import '../../../shared/components/multi_state/multi_state_view.dart';
import 'enterprise_detail_view.dart';

@RoutePage()
class EnterpriseDetailScreen extends MultiStateWidget<EnterpriseDetailLogic> {
  final String entId;

  const EnterpriseDetailScreen({
    super.key,
    required this.entId,
  }) : super(
          useScaffold: false, // 我们自己管理Scaffold
        );

  @override
  EnterpriseDetailLogic createController() {
    return EnterpriseDetailLogic(
        repository: locator<EnterpriseDetailRepository>(), entId: entId);
  }

  @override
  Widget buildBody(BuildContext context, EnterpriseDetailLogic logic) {
    // 智能状态映射：优先考虑加载状态，避免闪现空数据界面
    MultiViewState currentState;

    if (logic.viewState.isLoading()) {
      // 正在加载
      currentState = MultiViewState.loading;
    } else if (logic.viewState.isError()) {
      // 加载出错
      currentState = MultiViewState.error;
    } else if (logic.viewState.isFail()) {
      // 网络失败
      currentState = MultiViewState.network;
    } else if (logic.viewState.isSuccess()) {
      // 加载成功，检查是否有数据
      if (logic.detailEntity != null) {
        currentState = MultiViewState.success;
      } else {
        currentState = MultiViewState.empty;
      }
    } else if (logic.viewState.isEmpty()) {
      // 明确的空数据状态
      currentState = MultiViewState.empty;
    } else {
      // 默认状态：如果没有明确状态，显示加载
      currentState = MultiViewState.loading;
    }

    // 创建状态控制器
    final stateController = MultiStateController(currentState);

    return Scaffold(
      backgroundColor: Color(0xFF252C44),
      body: MultiStateView(
        controller: stateController,
        transparentBackground: true,
        animationDuration: Duration(milliseconds: 400),
        onRetry: () => logic.loadData(),
        onRefresh: () => logic.loadData(),

        // 成功状态 - 显示实际内容（只有在有数据时才会调用）
        contentWidget: logic.detailEntity != null
            ? _buildMainContent(context, logic)
            : SizedBox.shrink(),
        // 防御性编程

        // 自定义状态Widget
        loadingWidget: _buildCustomLoading(context),
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, EnterpriseDetailLogic logic) {
    final panelHeightOpen = MediaQuery.of(context).size.height - 80;
    const panelHeightClosed = 110.0;
    final enterDetail = logic.detailEntity;

    // 防御性检查
    if (enterDetail == null) {
      return SizedBox.shrink();
    }

    return SlidingUpPanel(
      minHeight: panelHeightClosed,
      maxHeight: panelHeightOpen,
      parallaxEnabled: true,
      parallaxOffset: .5,
      panelBuilder: (sc) => JobListDrawer(
          scrollController: sc, enterpriseDetailEntity: enterDetail),
      body: MainContent(enterpriseDetailEntity: enterDetail),
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(16.0),
        topRight: Radius.circular(16.0),
      ),
      color: AppColors.color_background,
    );
  }

  Widget _buildCustomLoading(BuildContext context) {
    return Center(
      child: Container(
        margin: EdgeInsets.fromLTRB(
            0, MediaQuery.of(context).viewPadding.top + 44.w, 0, 0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: CircularProgressIndicator(color: AppColors.white,strokeWidth: 3.w,),
            ),
            SizedBox(height: 12),
            Text(
              '正在加载...',
              style: TextStyle(
                  fontSize: 13.w, color: Colors.white.withValues(alpha: 1.0)),
            ),
          ],
        ),
      ),
    );
  }
}
