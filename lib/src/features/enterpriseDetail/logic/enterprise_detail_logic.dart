import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/enterprise_detail_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/enterprise_detail_repository.dart';

class EnterpriseDetailLogic extends ViewStateLogic {
  final EnterpriseDetailRepository repository;
  final String entId;
  EnterpriseDetailEntity? _detailEntity;

  EnterpriseDetailEntity? get detailEntity => _detailEntity;

  EnterpriseDetailLogic({required this.repository, required this.entId});

  @override
  void loadData() {
    sendRequest<EnterpriseDetailEntity>(
        _fetchEnterpriseDetail(), successCallback: (data) {
      _detailEntity = data;
    });
  }

  Future<
      UnifiedResponse<EnterpriseDetailEntity>> _fetchEnterpriseDetail() async {
    return await repository.getContentData(entId);
  }

  Future<void> fetchData() async => loadData();
}
