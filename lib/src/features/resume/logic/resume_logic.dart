import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/features/resume/ui/widget/resume_view.dart';

/// 简历页面业务逻辑
class ResumeLogic extends ChangeNotifier {
  ResumeData? _resumeData;
  
  ResumeData? get resumeData => _resumeData;

  void init() {
    _loadResumeData();
  }

  /// 加载简历数据
  void _loadResumeData() {
    // 模拟加载简历数据
    _resumeData = ResumeData(
      name: "张三",
      gender: "男",
      birthYear: "1998年",
      jobStatus: "离职-随时到岗",
      avatarUrl: 'https://placehold.co/100x100/ff5b00/FFFFFF?text=头像',
      workExperiences: [
        WorkExperience(
          company: "某某科技有限公司",
          position: "UI设计师",
          period: "2022.07 - 至今",
          description: "1. 负责公司产品线的UI设计，包括移动端和Web端。\n2. 参与产品前期讨论，输出交互原型和高保真视觉稿。\n3. 制定和维护设计规范，确保产品视觉统一性。",
        ),
      ],
      educationExperiences: [
        EducationExperience(
          school: "某某美术学院",
          degree: "本科",
          major: "视觉传达设计",
          period: "2018.09 - 2022.06",
        ),
      ],
      personalAdvantage: "",
      certificates: [
        Certificate(
          name: "大学英语六级(CET-6)",
          date: "2021.06",
        ),
      ],
    );
    notifyListeners();
  }

  /// 更新个人信息
  void updatePersonalInfo({
    String? name,
    String? gender,
    String? birthYear,
    String? jobStatus,
    String? avatarUrl,
  }) {
    if (_resumeData != null) {
      _resumeData = ResumeData(
        name: name ?? _resumeData!.name,
        gender: gender ?? _resumeData!.gender,
        birthYear: birthYear ?? _resumeData!.birthYear,
        jobStatus: jobStatus ?? _resumeData!.jobStatus,
        avatarUrl: avatarUrl ?? _resumeData!.avatarUrl,
        workExperiences: _resumeData!.workExperiences,
        educationExperiences: _resumeData!.educationExperiences,
        personalAdvantage: _resumeData!.personalAdvantage,
        certificates: _resumeData!.certificates,
      );
      notifyListeners();
    }
  }

  /// 添加工作经历
  void addWorkExperience(WorkExperience experience) {
    if (_resumeData != null) {
      _resumeData!.workExperiences.add(experience);
      notifyListeners();
    }
  }

  /// 添加教育经历
  void addEducationExperience(EducationExperience education) {
    if (_resumeData != null) {
      _resumeData!.educationExperiences.add(education);
      notifyListeners();
    }
  }

  /// 更新个人优势
  void updatePersonalAdvantage(String advantage) {
    if (_resumeData != null) {
      _resumeData = ResumeData(
        name: _resumeData!.name,
        gender: _resumeData!.gender,
        birthYear: _resumeData!.birthYear,
        jobStatus: _resumeData!.jobStatus,
        avatarUrl: _resumeData!.avatarUrl,
        workExperiences: _resumeData!.workExperiences,
        educationExperiences: _resumeData!.educationExperiences,
        personalAdvantage: advantage,
        certificates: _resumeData!.certificates,
      );
      notifyListeners();
    }
  }

  /// 添加证书
  void addCertificate(Certificate certificate) {
    if (_resumeData != null) {
      _resumeData!.certificates.add(certificate);
      notifyListeners();
    }
  }

  /// 保存简历
  Future<void> saveResume() async {
    try {
      // TODO: 实现保存简历到服务器的逻辑
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求
      // 保存成功的处理
    } catch (e) {
      // 处理错误
      print('保存失败：$e');
    }
  }

  /// 刷新数据
  void refreshData() {
    _loadResumeData();
  }
}
