import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../theme/resume_theme.dart';

/// 通用选择器组件
class CommonPicker {
  /// 显示底部选择器
  static Future<String?> show({
    required BuildContext context,
    required String title,
    required List<String> options,
    String? currentValue,
  }) async {
    if (options.isEmpty) return null;

    return await showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return _PickerBottomSheet(
          title: title,
          options: options,
          currentValue: currentValue,
        );
      },
    );
  }
}

/// 选择器底部弹窗
class _PickerBottomSheet extends StatefulWidget {
  final String title;
  final List<String> options;
  final String? currentValue;

  const _PickerBottomSheet({
    required this.title,
    required this.options,
    this.currentValue,
  });

  @override
  State<_PickerBottomSheet> createState() => _PickerBottomSheetState();
}

class _PickerBottomSheetState extends State<_PickerBottomSheet> {
  late String selectedValue;
  late int initialIndex;

  @override
  void initState() {
    super.initState();
    
    // 找到当前值在选项中的索引
    initialIndex = 0;
    selectedValue = widget.options[0]; // 🔥 默认选择第一个选项
    
    if (widget.currentValue != null && widget.currentValue!.isNotEmpty) {
      final index = widget.options.indexOf(widget.currentValue!);
      if (index != -1) {
        initialIndex = index;
        selectedValue = widget.currentValue!; // 如果有当前值，使用当前值
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r), // 🔥 恢复原有的16.r圆角
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部 - 恢复原有的三段式布局
          _buildHeader(),
          // 分割线 - 恢复原有的分割线
          Container(height: 1.h, color: ResumeTheme.borderColor),
          // 选择器
          SizedBox(
            height: 200.h,
            child: CupertinoPicker(
              itemExtent: 40.h,
              scrollController: FixedExtentScrollController(
                initialItem: initialIndex,
              ),
              onSelectedItemChanged: (int index) {
                setState(() {
                  selectedValue = widget.options[index];
                });
              },
              children: widget.options.map((String value) {
                return Center(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: ResumeTheme.textPrimary,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建头部 - 恢复原有的三段式布局
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h), // 🔥 恢复原有的padding
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 取消按钮
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              '取消',
              style: TextStyle(color: ResumeTheme.textSecondary), // 🔥 恢复原有的TextButton样式
            ),
          ),
          // 标题
          Text(
            widget.title,
            style: TextStyle(
              fontWeight: FontWeight.w600, // 🔥 恢复原有的字体样式
              color: ResumeTheme.textPrimary,
            ),
          ),
          // 确定按钮
          TextButton(
            onPressed: () => Navigator.pop(context, selectedValue),
            child: Text(
              '确定',
              style: TextStyle(color: ResumeTheme.primaryColor), // 🔥 恢复原有的TextButton样式
            ),
          ),
        ],
      ),
    );
  }

}
