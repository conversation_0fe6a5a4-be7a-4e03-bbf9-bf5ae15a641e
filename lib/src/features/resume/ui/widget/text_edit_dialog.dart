import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:math' as math;
import '../../theme/resume_theme.dart';

/// 文本编辑弹窗工具类
class TextEditDialog {
  /// 显示文本编辑弹窗 - 完全保持原有逻辑和样式
  static void show({
    required BuildContext context,
    required String title,
    required String initialText,
    required int maxLength,
    required int maxLines,
    required Function(String) onSave,
    String? hintText,
    double? heightRatio, // 自定义高度比例，默认0.9
  }) {
    // 🔥 预构建弹窗，避免在showModalBottomSheet中构建
    final bottomSheet = TextEditBottomSheet(
      title: title,
      initialText: initialText,
      maxLength: maxLength,
      maxLines: maxLines,
      onSave: onSave,
      hintText: hintText,
      heightRatio: heightRatio,
    );

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false, // 禁用点击外部关闭
      enableDrag: false,    // 禁用向下拖动关闭
      useSafeArea: false,   // 🔥 禁用安全区域计算，减少布局开销
      builder: (BuildContext context) => bottomSheet, // 🔥 直接返回预构建的widget
    );
  }
}

/// 文本编辑底部弹窗 - 完全保持原有代码
class TextEditBottomSheet extends StatefulWidget {
  final String title;
  final String initialText;
  final int maxLength;
  final int maxLines;
  final Function(String) onSave;
  final String? hintText;
  final double? heightRatio; // 自定义高度比例

  const TextEditBottomSheet({
    super.key,
    required this.title,
    required this.initialText,
    required this.maxLength,
    required this.maxLines,
    required this.onSave,
    this.hintText,
    this.heightRatio,
  });

  @override
  State<TextEditBottomSheet> createState() => _TextEditBottomSheetState();
}

class _TextEditBottomSheetState extends State<TextEditBottomSheet> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late ScrollController _scrollController; // 🔥 添加滚动控制器
  int _currentLength = 0;
  double _previousKeyboardHeight = 0; // 记录上一次键盘高度
  bool _isKeyboardStable = true; // 键盘是否稳定（不在动画中）
  bool _isKeyboardFullyExpanded = false; // 键盘是否完全弹出
  double _targetPadding = 0; // 目标padding值
  bool _shouldAnimatePadding = false; // 是否应该执行padding动画

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();
    _scrollController = ScrollController(); // 🔥 初始化滚动控制器
    _currentLength = widget.initialText.length;

    // 🔥 只添加监听器，不自动弹出键盘
    _controller.addListener(() {
      if (mounted) {
        setState(() {
          _currentLength = _controller.text.length;
        });
      }
    });

    // 🔥 添加光标位置监听器
    _controller.addListener(_ensureCursorVisible);

    // 🔥 移除自动弹出键盘，让用户主动点击
    // 这样可以完全避免弹窗显示时的性能冲突
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _scrollController.dispose(); // 🔥 释放滚动控制器
    super.dispose();
  }

  /// 🔥 优化的键盘状态检测 - 分步动画处理
  void _updateKeyboardStability(double currentKeyboardHeight) {
    if (_previousKeyboardHeight != currentKeyboardHeight) {
      final bool isKeyboardRising = currentKeyboardHeight > _previousKeyboardHeight;
      final bool isKeyboardFalling = currentKeyboardHeight < _previousKeyboardHeight;

      setState(() {
        _previousKeyboardHeight = currentKeyboardHeight;
        _isKeyboardStable = false;
      });

      if (isKeyboardRising) {
        // 🔥 键盘弹起：分步处理
        _handleKeyboardRising(currentKeyboardHeight);
      } else if (isKeyboardFalling) {
        // 🔥 键盘收起：直接处理，无需分步
        _handleKeyboardFalling(currentKeyboardHeight);
      }
    }
  }

  /// 🔥 处理键盘弹起 - 分步动画
  void _handleKeyboardRising(double keyboardHeight) {
    // 第一步：等待键盘完全弹出
    Future.delayed(const Duration(milliseconds: 250), () {
      if (mounted && _previousKeyboardHeight == keyboardHeight) {
        // 第二步：检测键盘是否已经稳定
        setState(() {
          _isKeyboardFullyExpanded = true;
        });

        // 第三步：计算目标padding
        _calculateTargetPadding(keyboardHeight);

        // 第四步：延迟50ms后开始padding动画
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted) {
            setState(() {
              _shouldAnimatePadding = true;
              _isKeyboardStable = true;
            });

            // 🔥 第五步：确保光标在可见范围内
            Future.delayed(const Duration(milliseconds: 100), () {
              if (mounted) {
                _ensureCursorVisible();
              }
            });
          }
        });
      }
    });
  }

  /// 🔥 处理键盘收起 - 直接处理
  void _handleKeyboardFalling(double keyboardHeight) {
    // 键盘收起时直接处理，无需分步
    setState(() {
      _isKeyboardFullyExpanded = false;
      _shouldAnimatePadding = false;
      _targetPadding = keyboardHeight > 0
          ? keyboardHeight + 20.h
          : 20.h + MediaQuery.of(context).padding.bottom;
    });

    // 短暂延迟后标记为稳定
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _isKeyboardStable = true;
        });
      }
    });
  }

  /// 🔥 计算目标padding值
  void _calculateTargetPadding(double keyboardHeight) {
    final targetPadding = keyboardHeight > 0
        ? keyboardHeight + 20.h // 键盘弹出时：键盘高度 + 20px间距
        : 20.h + MediaQuery.of(context).padding.bottom; // 键盘收起时：正常间距 + 安全区域

    setState(() {
      _targetPadding = targetPadding;
    });
  }

  /// 🔥 优化的动画判断逻辑
  bool _shouldUseAnimation() {
    // 键盘弹起时：只有在完全弹出且应该执行padding动画时才使用动画
    // 键盘收起时：直接使用动画
    return _isKeyboardStable && (_shouldAnimatePadding || !_isKeyboardFullyExpanded);
  }

  @override
  Widget build(BuildContext context) {
    final heightRatio = widget.heightRatio ?? 0.9; // 默认90%高度

    // 🔥 缓存MediaQuery，避免重复计算
    final mediaQuery = MediaQuery.of(context);

    return Container(
      height: mediaQuery.size.height * heightRatio, // 自定义高度比例
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 16.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: ResumeTheme.textPrimary,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(4.w),
              child: Icon(
                Icons.close,
                size: 24.w,
                color: ResumeTheme.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 优化的内容区域构建 - 分步动画
  Widget _buildContent() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    // 检测键盘状态变化
    _updateKeyboardStability(keyboardHeight);

    return Padding(
      padding: EdgeInsets.fromLTRB(10.w, 0, 10.w, 0),
      child: Column(
        children: [
          // 🔥 文本输入框 - 支持滚动和光标可见性
          Expanded(
            child: Container(
              padding: EdgeInsets.all(16.w),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                maxLength: widget.maxLength,
                maxLines: null,
                expands: true,
                scrollController: _scrollController, // 🔥 添加滚动控制器
                cursorColor: ResumeTheme.primaryColor, // 🔥 设置光标颜色为主题色
                textAlignVertical: TextAlignVertical.top,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ResumeTheme.textPrimary,
                ),
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero, // 🔥 移除内部padding，使用外部Container的padding
                  counterText: '',
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: const Color(0xFFCCCCCC),
                    fontSize: 16.sp,
                  ),
                ),
              ),
            ),
          ),
          // 🔥 优化的分步动画底部操作栏
          AnimatedPadding(
            duration: _shouldUseAnimation()
                ? const Duration(milliseconds: 250) // 🔥 分步动画时使用较长时间
                : Duration.zero, // 🔥 禁用动画时立即变化
            curve: Curves.easeOutQuart, // 🔥 更平滑的缓动曲线
            padding: EdgeInsets.only(
              left: 0,
              right: 0,
              top: 20.h,
              bottom: _getBottomPadding(keyboardHeight),
            ),
            child: _buildBottomBar(),
          ),
        ],
      ),
    );
  }

  /// 🔥 获取底部padding - 支持分步动画
  double _getBottomPadding(double keyboardHeight) {
    if (_shouldAnimatePadding && _targetPadding > 0) {
      // 使用计算好的目标padding
      return _targetPadding;
    } else if (keyboardHeight > 0) {
      // 键盘弹出但还未完成分步动画时，暂时不调整padding
      return _isKeyboardFullyExpanded
          ? keyboardHeight + 20.h
          : 20.h + MediaQuery.of(context).padding.bottom;
    } else {
      // 键盘收起时的正常padding
      return 20.h + MediaQuery.of(context).padding.bottom;
    }
  }

  /// 🔥 确保光标在可见范围内 - 简化实现
  void _ensureCursorVisible() {
    if (!_focusNode.hasFocus || !mounted) return;

    // 延迟执行，确保布局完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || !_focusNode.hasFocus || !_scrollController.hasClients) return;

      final selection = _controller.selection;
      if (!selection.isValid) return;

      // 🔥 简化方案：计算光标大概位置
      final text = _controller.text;
      final cursorPosition = selection.baseOffset;

      // 计算光标前的文本行数
      final textBeforeCursor = text.substring(0, cursorPosition);
      final lines = textBeforeCursor.split('\n');
      final currentLine = lines.length - 1;

      // 估算行高和光标位置
      final lineHeight = 24.h; // 行高估算
      final estimatedCursorY = currentLine * lineHeight;

      // 计算可见区域高度
      final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      final containerHeight = MediaQuery.of(context).size.height * (widget.heightRatio ?? 0.9);
      final headerHeight = 60.h;
      final bottomBarHeight = 60.h;
      final paddingHeight = _getBottomPadding(keyboardHeight);

      final availableHeight = containerHeight - headerHeight - bottomBarHeight - paddingHeight;

      // 检查是否需要滚动
      final currentScrollOffset = _scrollController.offset;
      final visibleTop = currentScrollOffset;
      final visibleBottom = currentScrollOffset + availableHeight;

      if (estimatedCursorY < visibleTop) {
        // 光标在可见区域上方，向上滚动
        final targetOffset = math.max(0.0, estimatedCursorY - lineHeight).toDouble();
        _scrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOutCubic,
        );
      } else if (estimatedCursorY > visibleBottom - lineHeight * 2) {
        // 光标在可见区域下方，向下滚动
        final targetOffset = estimatedCursorY - availableHeight + lineHeight * 3;
        final maxOffset = _scrollController.position.maxScrollExtent;
        _scrollController.animateTo(
          math.min(targetOffset, maxOffset).toDouble(),
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOutCubic,
        );
      }
    });
  }

  /// 构建底部操作栏 - 完全保持原有代码
  Widget _buildBottomBar() {
    return Row(
      children: [
        const Spacer(),
        // 字数统计 - 极简实现，减少渲染开销
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$_currentLength',
              style: TextStyle(
                fontSize: 12.sp,
                color: _currentLength >= widget.maxLength
                    ? ResumeTheme.primaryColor
                    : ResumeTheme.textSecondary,
              ),
            ),
            Text(
              '/',
              style: TextStyle(
                fontSize: 12.sp,
                color: ResumeTheme.textSecondary,
              ),
            ),
            Text(
              '${widget.maxLength}',
              style: TextStyle(
                fontSize: 12.sp,
                color: ResumeTheme.primaryColor,
              ),
            ),
          ],
        ),
        SizedBox(width: 14.w),
        // 保存按钮
        GestureDetector(
          onTap: _currentLength <= widget.maxLength ? _handleSave : null,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: _currentLength <= widget.maxLength
                  ? ResumeTheme.primaryColor
                  : ResumeTheme.primaryColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24.r),
            ),
            child: Text(
              '保存',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 处理保存
  void _handleSave() {
    if (_controller.text.trim().isEmpty) {
      // 可以添加提示：内容不能为空
      return;
    }

    widget.onSave(_controller.text.trim());
    Navigator.of(context).pop();
  }
}
