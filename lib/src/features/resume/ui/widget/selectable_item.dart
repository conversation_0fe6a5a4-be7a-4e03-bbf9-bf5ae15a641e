import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../theme/resume_theme.dart';

/// 可选择项组件
class SelectableItem extends StatelessWidget {
  final String label;
  final String? value;
  final String? hint;
  final Color? hintColor;
  final bool isRequired;
  final VoidCallback onTap;

  const SelectableItem({
    super.key,
    required this.label,
    this.value,
    this.hint,
    this.hintColor,
    this.isRequired = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final bool hasValue = value != null && value!.isNotEmpty;
    
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标签
                  _buildLabel(),
                  SizedBox(height: 7.h),
                  // 值或提示
                  _buildValue(hasValue),
                ],
              ),
            ),
            SizedBox(width: 8.w), // 添加一些间距
            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              color: ResumeTheme.textSecondary,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标签
  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 12.sp,
          color: ResumeTheme.textSecondary,
          fontFamily: 'Inter',
        ),
        children: [
          if (isRequired)
            TextSpan(
              text: '* ',
              style: TextStyle(color: ResumeTheme.requiredColor),
            ),
          TextSpan(text: label),
        ],
      ),
    );
  }

  /// 构建值或提示
  Widget _buildValue(bool hasValue) {
    return Text(
      hasValue ? value! : hint ?? '',
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: hasValue 
            ? ResumeTheme.textPrimary 
            : (hintColor ?? ResumeTheme.textPlaceholder),
      ),
    );
  }
}
