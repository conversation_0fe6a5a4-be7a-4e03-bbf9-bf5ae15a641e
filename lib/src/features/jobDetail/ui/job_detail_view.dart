//页面头部

import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/core/routing/app_router.dart';
import 'package:flutter_kit/src/datasource/models/job_detail_entity.dart';
import 'package:flutter_kit/src/shared/components/rich_text_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_color.dart';
import '../../enterpriseDetail/ui/enterprise_detail_screen.dart';

// 3. 字体规范 (Typography)
class AppTextStyles {
  static const String fontFamily = 'Noto Sans SC';

  static const TextStyle h1 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static const TextStyle salary = TextStyle(
    fontFamily: fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );

  static const TextStyle salarySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.primary,
  );

  static const TextStyle h2 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static const TextStyle body = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    color: AppColors.textBody,
    height: 1.8,
  );

  static const TextStyle regularTag = TextStyle(
    fontFamily: fontFamily,
    fontSize: 13,
    color: AppColors.textBody,
  );

  static const TextStyle smallTag = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    color: AppColors.textBody,
  );

  static const TextStyle footerButton = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.color_f02e4b
  );
}

// --- 自定义组件 (Widgets) ---

class _AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;

  const _AppCard({required this.child, this.padding});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: child,
    );
  }
}

class JobTitleCard extends StatelessWidget {
  final JobDetailEntity jobDetailEntity;

  const JobTitleCard({required this.jobDetailEntity});

  @override
  Widget build(BuildContext context) {
    return _AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(jobDetailEntity.name, style: AppTextStyles.h1),
          const SizedBox(height: 8),
          Text("${jobDetailEntity.pay} ${jobDetailEntity.jobPayUnit}",
              style: AppTextStyles.salary),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              InfoTag("汕头·龙湖区"),
              InfoTag("3-5年"),
              InfoTag("本科"),
            ],
          ),
        ],
      ),
    );
  }
}

class InfoTag extends StatelessWidget {
  final String text;

  const InfoTag(this.text);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.pageBackground,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(text, style: AppTextStyles.regularTag),
    );
  }
}

class JobDescriptionCard extends StatelessWidget {
  final JobDetailEntity jobDetailEntity;

  const JobDescriptionCard({required this.jobDetailEntity});

  @override
  Widget build(BuildContext context) {
    return _AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text("职位描述", style: AppTextStyles.h2),
          const SizedBox(height: 12),
          RichTextWidget(htmlContent: jobDetailEntity.describe,baseStyle: AppTextStyles.body,),
          // Text(
          //   jobDetailEntity.describe,
          //   style: AppTextStyles.body,
          // ),

        ],
      ),
    );
  }
}

class JobHighlightsCard extends StatelessWidget {
  const JobHighlightsCard();

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> benefits = [
      {'icon': Icons.security, 'text': '五险一金'},
      {'icon': Icons.card_giftcard, 'text': '年终奖'},
      {'icon': Icons.watch_later_outlined, 'text': '周末双休'},
      {'icon': Icons.beach_access_outlined, 'text': '带薪年假'},
      {'icon': Icons.health_and_safety_outlined, 'text': '定期体检'},
      {'icon': Icons.shopping_bag_outlined, 'text': '购物福利'},
      {'icon': Icons.free_breakfast_outlined, 'text': '下午茶'},
    ];

    return _AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text("职位亮点", style: AppTextStyles.h2),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 8,
              mainAxisSpacing: 16,
            ),
            itemCount: benefits.length,
            itemBuilder: (context, index) {
              return BenefitItem(
                icon: benefits[index]['icon'],
                text: benefits[index]['text'],
              );
            },
          ),
        ],
      ),
    );
  }
}

class BenefitItem extends StatelessWidget {
  final IconData icon;
  final String text;

  const BenefitItem({required this.icon, required this.text});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: AppColors.textSecondary, size: 28),
        const SizedBox(height: 8),
        Text(text, style: AppTextStyles.smallTag),
      ],
    );
  }
}

class CompanyInfoCard extends StatelessWidget {
  final JobDetailEntity jobDetailEntity;

  const CompanyInfoCard({required this.jobDetailEntity});

  @override
  Widget build(BuildContext context) {
    return _AppCard(
      padding: EdgeInsets.zero,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
            child: Column(
              children: [
                InkWell(
                  onTap: (){
                    context.router.push(EnterpriseDetailRoute(entId: jobDetailEntity.enterpriseId));
                    // context.router.pushWidget(
                    //   EnterpriseDetailScreen(entId: jobDetailEntity.enterpriseId),
                    //   transitionBuilder: (context, animation, secondaryAnimation, child) {
                    //     return SlideTransition(
                    //       position: Tween<Offset>(
                    //         begin: const Offset(1.0, 0.0),
                    //         end: Offset.zero,
                    //       ).animate(CurvedAnimation(
                    //         parent: animation,
                    //         curve: Curves.easeInOut,
                    //       )),
                    //       child: child,
                    //     );
                    //   },
                    // );

                  },
                  child:
                  Row(
                    children: [
                      CircleAvatar(
                          backgroundImage:
                          NetworkImage(jobDetailEntity.enterpriseLogoSmall),
                          radius: 15.w),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(jobDetailEntity.enterpriseName,
                                style: AppTextStyles.h2),
                            const SizedBox(height: 4),
                            Text(
                              "互联网 / 已上市 / 100人以上",
                              style: AppTextStyles.smallTag
                                  .copyWith(color: AppColors.textSecondary),
                            ),
                          ],
                        ),
                      ),
                      const Icon(Icons.arrow_forward_ios,
                          color: Colors.grey, size: 16),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                const Divider(color: AppColors.pageBackground, height: 1),
                const SizedBox(height: 12),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.location_on_outlined,
                        color: AppColors.textSecondary, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        jobDetailEntity.workAddress,
                        style: AppTextStyles.body,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          ClipRRect(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
          ),
        ],
      ),
    );
  }
}

class OtherJobsSection extends StatelessWidget {
  const OtherJobsSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: Text("其他在招职位", style: AppTextStyles.h2),
        ),
        const SizedBox(height: 4),
        const OtherJobCard(
          title: "UI设计师",
          salary: "15000-25000 元/月",
          company: "风速网络科技 · 澄海区",
          logoUrl: "https://placehold.co/32x32/34d399/ffffff?text=速",
          tags: ["扁平管理", "下午茶", "弹性工作"],
        ),
        const SizedBox(height: 12),
        const OtherJobCard(
          title: "Java后端开发",
          salary: "20000-35000 元/月",
          company: "云端智能集团 · 金平区",
          logoUrl: "https://placehold.co/32x32/60a5fa/ffffff?text=云",
          tags: ["大厂背景", "技术驱动", "六险一金"],
        ),
      ],
    );
  }
}

class OtherJobCard extends StatelessWidget {
  final String title;
  final String salary;
  final String company;
  final String logoUrl;
  final List<String> tags;

  const OtherJobCard({
    required this.title,
    required this.salary,
    required this.company,
    required this.logoUrl,
    required this.tags,
  });

  @override
  Widget build(BuildContext context) {
    return _AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title, style: AppTextStyles.h2.copyWith(fontSize: 15)),
              Text(salary, style: AppTextStyles.salarySmall),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              CircleAvatar(
                  backgroundImage: NetworkImage(logoUrl), radius: 15.w),
              const SizedBox(width: 8),
              Text(company, style: AppTextStyles.regularTag),
            ],
          ),
          const SizedBox(height: 12),
          const Divider(color: AppColors.pageBackground, height: 1),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: tags.map((tag) => InfoTag(tag)).toList(),
          ),
        ],
      ),
    );
  }
}

class FooterBar extends StatelessWidget {
  const FooterBar();

  @override
  Widget build(BuildContext context) {
    // 获取底部安全区域高度
    final bottomSafeArea = MediaQuery.of(context).viewPadding.bottom;

    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          height: 80 + bottomSafeArea, // 添加底部安全距离
          padding: EdgeInsets.fromLTRB(16, 10, 16, 10 + bottomSafeArea), // 底部padding包含安全距离
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            border: const Border(
                top: BorderSide(color: AppColors.pageBackground, width: 1)),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 1,
                child: SizedBox(
                  height: 48,
                  child: TextButton(
                    onPressed: () {},
                    style: TextButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      foregroundColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child:
                        const Text("和TA沟通", style: AppTextStyles.footerButton),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Ink(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFF7185), AppColors.primary],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                          const Text("立即投递",
                              style: TextStyle(color: AppColors.color_white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold )),
                        ],)
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
