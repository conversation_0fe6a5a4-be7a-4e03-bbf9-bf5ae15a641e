import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../shared/components/skeleton/skeleton_widgets.dart';

/// 职位详情页骨架屏
class JobDetailSkeleton extends StatelessWidget {
  const JobDetailSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CustomScrollView(
          slivers: [
            SliverPadding(
              padding: EdgeInsets.fromLTRB(0, 0, 0, 80 + MediaQuery.of(context).viewPadding.bottom + 20),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  const SizedBox(height: 10),
                  // 职位标题卡片骨架屏
                  _JobTitleCardSkeleton(),
                  const SizedBox(height: 20),
                  // 职位描述卡片骨架屏
                  _JobDescriptionCardSkeleton(),
                  const SizedBox(height: 20),
                  // 职位亮点卡片骨架屏
                  _JobHighlightsCardSkeleton(),
                  const SizedBox(height: 20),
                  // 公司信息卡片骨架屏
                  _CompanyInfoCardSkeleton(),
                  const SizedBox(height: 20),
                ]),
              ),
            ),
          ],
        ),
        // 底部按钮栏骨架屏
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: _FooterBarSkeleton(),
        ),
      ],
    );
  }
}

/// 职位标题卡片骨架屏
class _JobTitleCardSkeleton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return AnimatedSkeleton(
      child: SkeletonWidgets.card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 职位标题
            SkeletonWidgets.textLine(width: 200.w, height: 24.w),
            SizedBox(height: 12.w),
            // 薪资
            SkeletonWidgets.textLine(width: 120.w, height: 20.w),
            SizedBox(height: 16.w),
            // 标签行
            Row(
              children: [
                SkeletonWidgets.tag(width: 60.w),
                SizedBox(width: 8.w),
                SkeletonWidgets.tag(width: 80.w),
                SizedBox(width: 8.w),
                SkeletonWidgets.tag(width: 70.w),
              ],
            ),
            SizedBox(height: 16.w),
            // 公司信息行
            Row(
              children: [
                SkeletonWidgets.circle(size: 40.w),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonWidgets.textLine(width: 150.w, height: 16.w),
                      SizedBox(height: 4.w),
                      SkeletonWidgets.textLine(width: 100.w, height: 14.w),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 职位描述卡片骨架屏
class _JobDescriptionCardSkeleton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return AnimatedSkeleton(
      child: SkeletonWidgets.card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            SkeletonWidgets.textLine(width: 100.w, height: 18.w),
            SizedBox(height: 16.w),
            // 描述文本行
            for (int i = 0; i < 5; i++) ...[
              SkeletonWidgets.textLine(
                width: i == 4 ? 180.w : null,
                height: 16.w,
              ),
              SizedBox(height: 8.w),
            ],
          ],
        ),
      ),
    );
  }
}

/// 职位亮点卡片骨架屏
class _JobHighlightsCardSkeleton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return AnimatedSkeleton(
      child: SkeletonWidgets.card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            SkeletonWidgets.textLine(width: 100.w, height: 18.w),
            SizedBox(height: 16.w),
            // 福利项目网格
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 3,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.w,
              ),
              itemCount: 6,
              itemBuilder: (context, index) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      SkeletonWidgets.rectangle(width: 20.w, height: 20.w),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: SkeletonWidgets.textLine(height: 14.w),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// 公司信息卡片骨架屏
class _CompanyInfoCardSkeleton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return AnimatedSkeleton(
      child: SkeletonWidgets.card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 公司头部信息
            Row(
              children: [
                SkeletonWidgets.circle(size: 60.w),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonWidgets.textLine(width: 150.w, height: 18.w),
                      SizedBox(height: 8.w),
                      Row(
                        children: [
                          SkeletonWidgets.tag(width: 50.w, height: 20.w),
                          SizedBox(width: 8.w),
                          SkeletonWidgets.tag(width: 60.w, height: 20.w),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.w),
            // 公司描述
            SkeletonWidgets.textLine(width: 80.w, height: 16.w),
            SizedBox(height: 12.w),
            for (int i = 0; i < 3; i++) ...[
              SkeletonWidgets.textLine(
                width: i == 2 ? 200.w : null,
                height: 14.w,
              ),
              SizedBox(height: 6.w),
            ],
          ],
        ),
      ),
    );
  }
}

/// 底部按钮栏骨架屏
class _FooterBarSkeleton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final bottomSafeArea = MediaQuery.of(context).viewPadding.bottom;
    
    return AnimatedSkeleton(
      child: Container(
        height: 80 + bottomSafeArea,
        padding: EdgeInsets.fromLTRB(16, 10, 16, 10 + bottomSafeArea),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          border: Border(
            top: BorderSide(color: Colors.grey[300]!, width: 1),
          ),
        ),
        child: Row(
          children: [
            SizedBox(width: 16.w),
            Expanded(
              child: SkeletonWidgets.button(height: 48.w),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: SkeletonWidgets.button(height: 48.w),
            ),
          ],
        ),
      ),
    );
  }
}
