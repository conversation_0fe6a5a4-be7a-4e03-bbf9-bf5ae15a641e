



import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/job_detail_entity.dart';
import '../../../datasource/repositories/job_detail_repository.dart';

class JobDetailLogic extends ViewStateLogic {
  final JobDetailRepository repository;
  final String jobId;
  JobDetailEntity? _jobDetail;

  JobDetailEntity? get jobDetail => _jobDetail;

  JobDetailLogic({
    required this.repository,
    required this.jobId,
  });

  @override
  void loadData() {
    sendRequest<JobDetailEntity>(_fetchJobDetail(), successCallback: (data) {
      _jobDetail = data;
    });
  }

  Future<UnifiedResponse<JobDetailEntity>> _fetchJobDetail() async {
    return await repository.getContentData(jobId);
  }

  Future<void> fetchData() async => loadData();
}