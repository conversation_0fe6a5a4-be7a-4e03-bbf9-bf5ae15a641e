import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/widgets/view_state_widget.dart';
import 'package:flutter_kit/src/datasource/repositories/login_repository.dart';
import 'package:flutter_kit/src/features/login/logic/login_logic.dart';
import 'package:flutter_kit/src/features/login/ui/login_view.dart';
import 'package:flutter_kit/src/shared/locator.dart';


/// 登录页面
///
/// 功能特性：
/// - 统一的透明AppBar设计
/// - 专业的登录UI界面
/// - 完整的表单验证
/// - 图形验证码支持
/// - 用户协议勾选
@RoutePage()
class LoginScreen extends ViewStateWidget<LoginLogic> {
  const LoginScreen({super.key}) : super(
    autoLoad: false, // 登录页面不需要自动加载数据
    bindViewState: false, // 登录页面不需要自动状态管理，由LoginView处理
  );

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, LoginLogic logic) {
    return null;
  }

  @override
  Widget buildBody(BuildContext context, LoginLogic logic) {
    // 集成登录UI组件，传入业务逻辑控制器
    return LoginView(logic: logic);
  }

  @override
  LoginLogic createController() {
    // 使用依赖注入获取LoginRepository
    // 确保与项目架构规范保持一致
    return LoginLogic(repository: locator<LoginRepository>());
  }

  @override
  bool resizeToAvoidBottomInset() => true; // 避免键盘遮挡输入框

  @override
  Color? backgroundColor() => null; // 使用默认背景色，由LoginView处理渐变
}