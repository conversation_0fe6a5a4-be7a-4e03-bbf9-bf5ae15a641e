
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_kit/src/features/login/ui/widgets/captcha_widget.dart';
import 'package:flutter_kit/src/shared/components/app_bars/index.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_color.dart';
import '../../../shared/components/buttons/button.dart';
import '../../../shared/components/forms/input.dart';
import '../logic/login_logic.dart';
import 'widgets/sms_verification_widget.dart';

/// 登录页面UI组件
class LoginView extends StatefulWidget {
  final LoginLogic logic;

  const LoginView({
    super.key,
    required this.logic,
  });

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _captchaController = TextEditingController();


  // 手势识别器 - 需要手动管理生命周期
  late final TapGestureRecognizer _userAgreementRecognizer;
  late final TapGestureRecognizer _privacyPolicyRecognizer;

  bool _isPasswordVisible = false;
  bool _isAgreedToTerms = false;
  String _captchaIdent = '';
  bool _isSmsLogin = false; // 是否为验证码登录模式
  bool _showSmsVerification = false; // 是否显示验证码输入界面

  // 验证码图片相关状态
  String _captchaImageBase64 = ''; // 验证码图片的base64字符串
  bool _isLoadingCaptcha = false; // 是否正在加载验证码

  // 按钮状态（与验证码状态分离）
  bool _hasUserInput = false; // 用户是否有输入



  @override
  void initState() {
    super.initState();

    // 初始化手势识别器
    _userAgreementRecognizer = TapGestureRecognizer()
      ..onTap = _showUserAgreement;
    _privacyPolicyRecognizer = TapGestureRecognizer()
      ..onTap = _showPrivacyPolicy;

  }

  @override
  void dispose() {
    // 释放控制器
    _phoneController.dispose();
    _passwordController.dispose();
    _captchaController.dispose();


    // 释放手势识别器 - 防止内存泄漏
    _userAgreementRecognizer.dispose();
    _privacyPolicyRecognizer.dispose();



    super.dispose();
  }

  /// 生成验证码标识
  /// 注意：此方法会触发 setState，可能影响输入框焦点
  /// 现在只在以下情况调用：
  /// 1. 页面初始化时
  /// 2. 用户手动点击验证码图片时
  void _generateCaptcha() {
    _captchaIdent = DateTime.now().millisecondsSinceEpoch.toString();
    // 生成新的验证码标识后，加载验证码图片
    _loadCaptchaImage();
  }



  /// 加载验证码图片
  void _loadCaptchaImage() async {
    print("调用了 _loadCaptchaImage");
    // 避免重复加载
    if (_isLoadingCaptcha) return;

    setState(() {
      _isLoadingCaptcha = true;
    });

    try {
      // 调用接口获取验证码图片
      final response = await widget.logic.getImage('80', '32', 0);

      if (mounted) {
        if (response.isSuccess && response.data != null) {
          setState(() {
            _captchaImageBase64 = response.data!;
            _isLoadingCaptcha = false;
          });
        } else {
          setState(() {
            _captchaImageBase64 = '';
            _isLoadingCaptcha = false;
          });
          _showMessage('验证码加载失败，请重试');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _captchaImageBase64 = '';
          _isLoadingCaptcha = false;
        });
        _showMessage('验证码加载失败，请重试');
        print('加载验证码失败: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      body: GestureDetector(
        onTap: () {
        },
        child: Stack(
          children: [
            // 背景渐变
            _buildBackgroundGradient(),
            // 主要内容
            _buildMainContent(),
            //头部titlebar
            _buildTitleBar(),
          ],
        ),
      ),
    );
  }

  /// 构建titlebar
  Widget _buildTitleBar() {
    return Positioned(
        top: 0,
        left: 0,
        right: 0,
        child: UnifiedAppBar.customTitleBar(
            title: '',
            onBackPressed: _handleBackPressed,
            iconColor: AppColors.color_666666,backgroundColor: Colors.transparent));
  }

  /// 处理返回按钮点击
  void _handleBackPressed() {
    if (_showSmsVerification) {
      // 如果在验证码输入界面，返回到手机号输入界面
      setState(() {
        _showSmsVerification = false;
      });
    } else {
      // 如果在手机号输入界面，退出当前页面
      context.router.pop();
    }
  }

  /// 构建背景渐变
  Widget _buildBackgroundGradient() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 300.h,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary.withValues(alpha: 0.08),
              AppColors.primary.withValues(alpha: 0.04),
              AppColors.primary.withValues(alpha: 0.02),
              Colors.transparent,
            ],
            stops: const [0.0, 0.4, 0.7, 1.0],
          ),
        ),
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 60.h),
            // 根据状态显示不同的界面
            if (_showSmsVerification)
              _buildSmsVerificationContent()
            else
              _buildLoginContent(),
            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }

  /// 构建登录内容
  Widget _buildLoginContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        SizedBox(height: 60.h),
        _buildLoginForm(),
        SizedBox(height: 40.h),
        _buildTermsAgreement(),
        SizedBox(height: 12.h),
        _buildLoginButton(),
        SizedBox(height: 20.h),
        _buildBottomActions(),
      ],
    );
  }

  /// 构建验证码输入内容
  Widget _buildSmsVerificationContent() {
    return SmsVerificationWidget(
      phoneNumber: _phoneController.text.trim(),
      onVerificationSuccess: _handleSmsVerificationSuccess,
      onBack: _handleBackToLogin,
    );
  }

  /// 构建头部标题
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主标题 - 推窗覆盖效果
        SizedBox(
          height: 40.h, // 固定高度避免布局跳动
          child: Stack(
            children: [
              // 底层文字（"欢迎登录"）- 验证码模式时隐藏
              Positioned(
                left: 0,
                top: 0,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 400),
                  opacity: _isSmsLogin ? 0.0 : 1.0, // 验证码模式时隐藏
                  child: Text(
                    '欢迎登录',
                    style: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                      height: 1.2,
                    ),
                  ),
                ),
              ),
              // 顶层文字（"验证码登录"）- 推窗效果
              Positioned(
                left: 0,
                top: 0,
                child: ClipRect(
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeInOutCubic,
                    width: _isSmsLogin ? 180.w : 0, // 验证码模式时推窗显示，密码模式时推窗隐藏
                    child: Text(
                      '验证码登录',
                      style: TextStyle(
                        fontSize: 32.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                        height: 1.2,
                      ),
                      overflow: TextOverflow.clip,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8.h),
        // 副标题 - 推窗覆盖效果
        SizedBox(
          height: 24.h, // 固定高度避免布局跳动
          child: Stack(
            children: [
              // 底层文字（"请输入您的账号信息"）- 验证码模式时隐藏
              Positioned(
                left: 0,
                top: 0,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 450),
                  opacity: _isSmsLogin ? 0.0 : 1.0, // 验证码模式时隐藏
                  child: Text(
                    '请输入您的账号信息',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textSecondary,
                      height: 1.4,
                    ),
                  ),
                ),
              ),
              // 顶层文字（"请输入手机号获取验证码"）- 推窗效果
              Positioned(
                left: 0,
                top: 0,
                child: ClipRect(
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 450), // 稍微延迟，形成层次感
                    curve: Curves.easeInOutCubic,
                    width: _isSmsLogin ? 280.w : 0, // 验证码模式时推窗显示，密码模式时推窗隐藏
                    child: Text(
                      '请输入手机号获取验证码',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.textSecondary,
                        height: 1.4,
                      ),
                      overflow: TextOverflow.clip,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          _buildPhoneInput(),
          AnimatedSize(
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOutQuart,
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 600),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: Tween<double>(
                    begin: 0.0,
                    end: 1.0,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: const Interval(0.2, 1.0, curve: Curves.easeOutQuart),
                  )),
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.0, -0.1),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: const Interval(0.1, 0.9, curve: Curves.easeOutQuart),
                    )),
                    child: child,
                  ),
                );
              },
              child: _isSmsLogin
                  ? const SizedBox.shrink(key: ValueKey('sms_form'))
                  : Column(
                      key: const ValueKey('password_form'),
                      children: [
                        SizedBox(height: 20.h),
                        _buildPasswordInput(),
                        SizedBox(height: 20.h),
                        _buildCaptchaInput(),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneInput() {
    return Input(
      controller: _phoneController,
      hintText: '请输入手机号',
      keyboardType: TextInputType.phone,
      textInputAction: _isSmsLogin ? TextInputAction.done : TextInputAction.next,
      prefixIcon: _isSmsLogin ? _buildCountryCodePrefix() : null,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入手机号';
        }
        if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
          return '请输入正确的手机号';
        }
        return null;
      },
      onChanged: (value) {
        // 只更新按钮状态，不影响验证码组件
        _updateUserInputState();
      },
      onSubmitted: (_) {
        if (!_isSmsLogin) {
          // 使用延迟确保焦点切换平滑
          Future.microtask(() {
          });
        }
      },
    );
  }

  /// 构建国家代码前缀
  Widget _buildCountryCodePrefix() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '+86',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 8.w),
          Container(
            width: 1.w,
            height: 20.h,
            color: AppColors.borderColor,
          ),
        ],
      ),
    );
  }

  /// 构建密码输入框
  Widget _buildPasswordInput() {
    return Input(
      controller: _passwordController,
      hintText: '请输入密码',
      isPassword: !_isPasswordVisible,
      textInputAction: TextInputAction.next,
      suffixIcon: IconButton(
        icon: Icon(
          _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          color: AppColors.textSecondary,
          size: 20.w,
        ),
        onPressed: () {
          setState(() {
            _isPasswordVisible = !_isPasswordVisible;
          });
        },
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入密码';
        }
        if (value.length < 6) {
          return '密码长度不能少于6位';
        }
        return null;
      },
      onChanged: (value) {
        // 只更新按钮状态，不影响验证码组件
        _updateUserInputState();
      },
      onSubmitted: (_) {
        // 使用延迟确保焦点切换平滑
        Future.microtask(() {
        });
      },
    );
  }

  /// 构建验证码输入框
  Widget _buildCaptchaInput() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Input(
            controller: _captchaController,
            hintText: '请输入验证码',
            textInputAction: TextInputAction.done,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入验证码';
              }
              return null;
            },
            onChanged: (value) {
              // 只更新按钮状态，不影响验证码组件
              _updateUserInputState();
            },
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          flex: 2,
          child: _buildCaptchaImage(),
        ),
      ],
    );
  }

  /// 构建验证码图片
  Widget _buildCaptchaImage() {
    // 使用 key 来保持组件稳定性，避免不必要的重建
    return CaptchaWidget(
      key: ValueKey('captcha_$_captchaIdent'),
      captchaIdent: _captchaIdent,
      onRefresh: _generateCaptcha,
      base64Image: _captchaImageBase64,
      isLoading: _isLoadingCaptcha,
      onLoadCaptcha: _loadCaptchaImage,
    );
  }

  /// 构建用户协议勾选
  Widget _buildTermsAgreement() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isAgreedToTerms = !_isAgreedToTerms;
            });
          },
          child: Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              color: _isAgreedToTerms ? AppColors.primary : Colors.white,
              border: _isAgreedToTerms
                  ? Border.all(
                      color: AppColors.primary,
                      width: 2,
                    )
                  : Border(
                      bottom: BorderSide(
                        color: AppColors.borderColor,
                        width: 2,
                      ),
                    ),
              borderRadius: BorderRadius.circular(4.w),
            ),
            child: _isAgreedToTerms
                ? Icon(
                    Icons.check,
                    size: 14.w,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
                height: 1.4,
              ),
              children: [
                const TextSpan(text: '已阅读并同意'),
                TextSpan(
                  text: '《用户协议》',
                  style: TextStyle(
                    color: AppColors.primary,
                  ),
                  recognizer: _userAgreementRecognizer,
                ),
                TextSpan(
                  text: '《隐私协议》',
                  style: TextStyle(
                    color: AppColors.primary,
                  ),
                  recognizer: _privacyPolicyRecognizer,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建登录按钮
  Widget _buildLoginButton() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200), // 缩短动画时长
      child: Button.primary(
        key: ValueKey(_isSmsLogin ? 'sms_button' : 'password_button'),
        title: _isSmsLogin ? '获取验证码' : '登录',
        onPressed: _shouldEnableButton() ? _handleLogin : null,
      ),
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Row(
      children: [
        // 左侧：切换登录方式 - 左对齐
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: _handleSmsLogin,
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 150), // 缩短动画时长
              child: Text(
                _isSmsLogin ? '密码登录' : '验证码登录',
                key: ValueKey(_isSmsLogin ? 'password_switch' : 'sms_switch'),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
        // 中间占位，推开左右两侧
        const Spacer(),
        // 右侧：忘记密码 - 右对齐
        Align(
          alignment: Alignment.centerRight,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: _isSmsLogin ? 0.0 : 1.0, // 验证码模式时透明度为0
            child: GestureDetector(
              onTap: _isSmsLogin ? null : _handleForgotPassword, // 透明时禁用点击
              child: Text(
                '忘记密码',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 检查按钮是否应该启用（只要有输入就启用）
  bool _shouldEnableButton() {
    return _hasUserInput;
  }

  /// 更新用户输入状态
  void _updateUserInputState() {
    bool hasInput;
    if (_isSmsLogin) {
      // 验证码登录模式：只要输入了手机号就启用
      hasInput = _phoneController.text.trim().isNotEmpty;
    } else {
      // 密码登录模式：只要三个字段都有输入就启用
      hasInput = _phoneController.text.trim().isNotEmpty &&
          _passwordController.text.trim().isNotEmpty &&
          _captchaController.text.trim().isNotEmpty;
    }

    // 只在状态真正改变时才调用 setState
    if (_hasUserInput != hasInput) {
      setState(() {
        _hasUserInput = hasInput;
      });
    }
  }

  /// 检查输入是否正确（点击时才调用）
  bool _validateInputs() {
    if (_isSmsLogin) {
      // 验证码登录模式：检查手机号格式
      return _phoneController.text.trim().isNotEmpty &&
          RegExp(r'^1[3-9]\d{9}$').hasMatch(_phoneController.text.trim());
    } else {
      // 密码登录模式：检查所有字段格式
      return _phoneController.text.trim().isNotEmpty &&
          RegExp(r'^1[3-9]\d{9}$').hasMatch(_phoneController.text.trim()) &&
          _passwordController.text.trim().isNotEmpty &&
          _passwordController.text.trim().length >= 6 &&
          _captchaController.text.trim().isNotEmpty;
    }
  }

  /// 处理登录
  void _handleLogin() {
    // 首先检查输入格式是否正确
    if (!_validateInputs()) {
      if (_isSmsLogin) {
        _showMessage('请输入正确的手机号');
      } else {
        _showMessage('请检查输入信息格式');
      }
      return;
    }

    // 检查表单验证
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 检查是否同意用户协议
    if (!_isAgreedToTerms) {
      _showAgreementDialog();
      return;
    }

    if (_isSmsLogin) {
      // 验证码登录模式：获取验证码
      _getSmsCode();
    } else {
      // 密码登录模式：直接登录
      _performLogin();
    }
  }

  /// 获取短信验证码
  void _getSmsCode() {
    // 检查是否同意用户协议
    if (!_isAgreedToTerms) {
      _showAgreementDialog();
      return;
    }

    // TODO: 调用获取验证码接口
    _showMessage('验证码已发送');
    setState(() {
      _showSmsVerification = true;
    });
  }

  /// 执行登录
  void _performLogin() {
    widget.logic
        .login(
      _phoneController.text.trim(),
      _passwordController.text.trim(),
      _captchaIdent,
      _captchaController.text.trim(),
    )
        .then((response) {
      if (response.isSuccess) {
        _showMessage('登录成功');
        // TODO: 跳转到主页面
      } else {
        _showMessage(response.errorMessage ?? '登录失败');
        // 移除自动刷新验证码，避免干扰输入框焦点
        // 用户需要手动点击验证码图片来刷新
      }
    }).catchError((error) {
      _showMessage('网络错误，请重试');
      // 移除自动刷新验证码，避免干扰输入框焦点
      // 用户需要手动点击验证码图片来刷新
    });
  }

  /// 处理短信验证成功
  void _handleSmsVerificationSuccess() {
    _showMessage('验证成功，登录成功');
    // TODO: 跳转到主页面
  }

  /// 处理返回登录界面
  void _handleBackToLogin() {
    setState(() {
      _showSmsVerification = false;
    });
  }

  /// 显示用户协议弹窗
  void _showAgreementDialog() {
    // 为弹窗创建临时的手势识别器
    final userAgreementRecognizer = TapGestureRecognizer()
      ..onTap = () {
        Navigator.of(context).pop();
        _showUserAgreement();
      };

    final privacyPolicyRecognizer = TapGestureRecognizer()
      ..onTap = () {
        Navigator.of(context).pop();
        _showPrivacyPolicy();
      };

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            '用户协议',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          content: RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              children: [
                const TextSpan(text: '请阅读并同意'),
                TextSpan(
                  text: '《用户协议》',
                  style: TextStyle(
                    color: AppColors.primary,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: userAgreementRecognizer,
                ),
                const TextSpan(text: '和'),
                TextSpan(
                  text: '《隐私协议》',
                  style: TextStyle(
                    color: AppColors.primary,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: privacyPolicyRecognizer,
                ),
                const TextSpan(text: '后继续使用'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 释放临时手势识别器
                userAgreementRecognizer.dispose();
                privacyPolicyRecognizer.dispose();
              },
              child: Text(
                '取消',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _isAgreedToTerms = true;
                });
                Navigator.of(context).pop();
                // 释放临时手势识别器
                userAgreementRecognizer.dispose();
                privacyPolicyRecognizer.dispose();
                // 同意协议后重新执行登录
                _handleLogin();
              },
              child: Text(
                '同意并继续',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    ).then((_) {
      // 确保弹窗关闭时释放手势识别器（防止其他方式关闭弹窗时泄漏）
      userAgreementRecognizer.dispose();
      privacyPolicyRecognizer.dispose();
    });
  }

  /// 显示用户协议
  void _showUserAgreement() {
    // TODO: 显示用户协议页面
    _showMessage('用户协议');
  }

  /// 显示隐私协议
  void _showPrivacyPolicy() {
    // TODO: 显示隐私协议页面
    _showMessage('隐私协议');
  }

  /// 处理验证码登录/密码登录切换
  void _handleSmsLogin() {
    setState(() {
      _isSmsLogin = !_isSmsLogin;
      _showSmsVerification = false; // 重置验证码界面状态
      // 切换模式时清空表单
      _phoneController.clear();
      _passwordController.clear();
      _captchaController.clear();
      // 移除自动生成验证码，避免干扰输入框焦点
      // 用户需要手动点击验证码图片来生成
    });
  }

  /// 处理忘记密码
  void _handleForgotPassword() {
    // TODO: 跳转到忘记密码页面
    _showMessage('忘记密码');
  }

  /// 显示消息提示
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.w),
        ),
      ),
    );
  }
}
