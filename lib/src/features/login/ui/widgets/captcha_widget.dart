import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/theme/dimens.dart';
import '../../../../shared/extensions/context_extensions.dart';
import '../../../../shared/utils/image_utils.dart';

/// 图形验证码组件
class CaptchaWidget extends StatefulWidget {
  final String captchaIdent;
  final VoidCallback onRefresh;
  final String base64Image; // base64编码的验证码图片
  final bool isLoading; // 是否正在加载
  final VoidCallback? onLoadCaptcha; // 加载验证码回调

  const CaptchaWidget({
    super.key,
    required this.captchaIdent,
    required this.onRefresh,
    this.base64Image = '',
    this.isLoading = false,
    this.onLoadCaptcha,
  });

  @override
  State<CaptchaWidget> createState() => _CaptchaWidgetState();
}

class _CaptchaWidgetState extends State<CaptchaWidget> {
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // 只在第一次初始化时加载，避免重复加载
    if (widget.base64Image.isEmpty && widget.onLoadCaptcha != null && !_hasInitialized) {
      _hasInitialized = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onLoadCaptcha!();
        }
      });
    }
  }

  @override
  void didUpdateWidget(CaptchaWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 防止因为父组件重建导致重复加载
    // 只有在 captchaIdent 真正改变时才重新加载
    if (oldWidget.captchaIdent != widget.captchaIdent &&
        widget.base64Image.isEmpty &&
        widget.onLoadCaptcha != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onLoadCaptcha!();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleRefresh,
      child: Container(
        height: 58.h,
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(Dimens.radius),
          border: Border.all(
            color: context.colorScheme.outline,
            width: 1.5,
          ),
        ),
        child: widget.isLoading
            ? _buildLoadingWidget()
            : _buildCaptchaContent(),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingWidget() {
    return Center(
      child: SizedBox(
        width: 20.w,
        height: 20.w,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      ),
    );
  }

  /// 构建验证码内容
  Widget _buildCaptchaContent() {
    return Stack(
      children: [
        // 验证码文字
        Center(
          child: _buildCaptchaImage(),
        ),
      ],
    );
  }

  /// 构建图片验证码
  Widget _buildCaptchaImage() {
    if (widget.base64Image.isEmpty) {
      return _buildPlaceholder();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(Dimens.radius),
      child: ImageUtils.base64ImageWithLoading(
        widget.base64Image,
        width: 120.w,
        height: 48.h,
        fit: BoxFit.cover,
        loadingWidget: _buildLoadingWidget(),
        errorWidget: _buildErrorWidget(),
      ),
    );
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    return Container(
      width: double.infinity,
      height: 48.h,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(Dimens.radius),
      ),
      child: Center(
        child: Text(
          '获取验证码',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorWidget() {
    return Container(
      width: double.infinity,
      height: 48.h,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(Dimens.radius),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.refresh,
              size: 16.w,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 2.h),
            Text(
              '点击重新加载',
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// 处理刷新
  void _handleRefresh() {
    // 直接调用刷新回调，加载状态由父组件管理
    widget.onRefresh();

    // 如果有加载回调，也调用它
    if (widget.onLoadCaptcha != null) {
      widget.onLoadCaptcha!();
    }
  }
}


