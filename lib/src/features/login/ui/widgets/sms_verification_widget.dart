import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_color.dart';

/// 短信验证码输入组件
class SmsVerificationWidget extends StatefulWidget {
  final String phoneNumber;
  final VoidCallback onVerificationSuccess;
  final VoidCallback onBack;

  const SmsVerificationWidget({
    super.key,
    required this.phoneNumber,
    required this.onVerificationSuccess,
    required this.onBack,
  });

  @override
  State<SmsVerificationWidget> createState() => _SmsVerificationWidgetState();
}

class _SmsVerificationWidgetState extends State<SmsVerificationWidget>
    with TickerProviderStateMixin {
  final TextEditingController _hiddenController = TextEditingController();
  final FocusNode _hiddenFocusNode = FocusNode();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  int _countdown = 60;
  bool _canResend = false;
  String _verificationCode = '';
  int _currentFocusIndex = 0;

  @override
  void initState() {
    super.initState();

    // 监听隐藏输入框的变化
    _hiddenController.addListener(_onHiddenTextChanged);

    // 初始化动画
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOutQuart),
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutQuart),
    ));

    // 启动动画
    _animationController.forward();

    // 启动倒计时
    _startCountdown();

    // 自动聚焦隐藏输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        FocusScope.of(context).requestFocus(_hiddenFocusNode);
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _hiddenController.dispose();
    _hiddenFocusNode.dispose();
    super.dispose();
  }

  /// 启动倒计时
  void _startCountdown() {
    setState(() {
      _countdown = 60;
      _canResend = false;
    });
    
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        setState(() {
          _countdown--;
          if (_countdown <= 0) {
            _canResend = true;
          }
        });
        return _countdown > 0;
      }
      return false;
    });
  }

  /// 处理隐藏输入框文本变化
  void _onHiddenTextChanged() {
    String text = _hiddenController.text;

    // 限制最多4位数字
    if (text.length > 4) {
      text = text.substring(0, 4);
      _hiddenController.value = _hiddenController.value.copyWith(
        text: text,
        selection: TextSelection.collapsed(offset: text.length),
      );
    }

    // 更新验证码和当前焦点位置
    _verificationCode = text;
    _currentFocusIndex = text.length < 4 ? text.length : 3;

    setState(() {});

    // 如果输入完4位数字，自动验证并收起键盘
    if (text.length == 4) {
      // 收起键盘
      _hiddenFocusNode.unfocus();
      Future.delayed(const Duration(milliseconds: 300), () {
        _verifyCode();
      });
    }
  }

  /// 强制请求焦点的方法，使用多种策略确保成功
  void _requestFocusWithRetry() {
    // 先取消当前焦点
    FocusScope.of(context).unfocus();

    // 延迟一帧后重新请求焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // 方法1: 使用 FocusScope
        FocusScope.of(context).requestFocus(_hiddenFocusNode);

        // 方法2: 如果方法1失败，延迟后再试一次
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && !_hiddenFocusNode.hasFocus) {
            _hiddenFocusNode.requestFocus();
          }
        });
      }
    });
  }





  /// 重新发送验证码
  void _resendCode() {
    if (_canResend) {
      // TODO: 调用重新发送验证码接口
      _startCountdown();
      _showMessage('验证码已重新发送');
    }
  }

  /// 验证验证码
  void _verifyCode() {
    if (_verificationCode.length == 4) {
      // TODO: 调用验证接口
      // 模拟验证成功
      _showMessage('验证成功');
      widget.onVerificationSuccess();
    } else {
      _showMessage('请输入完整的验证码');
    }
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.w),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_slideAnimation.value * MediaQuery.of(context).size.width, 0),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildContent(),
          ),
        );
      },
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 隐藏的输入框 - 放在顶部但不可见
        Opacity(
          opacity: 0.0,
          child: SizedBox(
            width: 1,
            height: 1,
            child: TextField(
              controller: _hiddenController,
              focusNode: _hiddenFocusNode,
              keyboardType: TextInputType.number,
              maxLength: 4,
              style: const TextStyle(color: Colors.transparent),
              decoration: const InputDecoration(
                border: InputBorder.none,
                counterText: '',
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              autofocus: false,
              enableInteractiveSelection: false,
            ),
          ),
        ),
        // 显示内容
        _buildHeader(),
        SizedBox(height: 40.h),
        _buildCodeDisplays(),
        SizedBox(height: 30.h),
        _buildResendSection(),
      ],
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '输入验证码',
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
            height: 1.2,
          ),
        ),
        SizedBox(height: 8.h),
        RichText(
          text: TextSpan(
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
              height: 1.4,
            ),
            children: [
              const TextSpan(text: '验证码已发送至 '),
              TextSpan(
                text: widget.phoneNumber,
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建验证码显示框
  Widget _buildCodeDisplays() {
    return GestureDetector(
      onTap: () {
        // 点击时强制聚焦隐藏输入框，使用多种方法确保成功
        _requestFocusWithRetry();
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(4, (index) {
          return _buildCodeDisplay(index);
        }),
      ),
    );
  }

  /// 构建单个验证码显示框
  Widget _buildCodeDisplay(int index) {
    String displayText = '';
    if (index < _verificationCode.length) {
      displayText = _verificationCode[index];
    }

    bool isFocused = index == _currentFocusIndex && _hiddenFocusNode.hasFocus;
    bool hasValue = displayText.isNotEmpty;

    return GestureDetector(
      onTap: () {
        // 点击任意框都可以强制聚焦隐藏输入框
        _requestFocusWithRetry();
      },
      child: Container(
        width: 64.w, // 固定宽度，确保所有框一样大
        height: 64.h, // 固定高度
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.w), // 增加圆角
          border: Border.all(
            color: isFocused
                ? AppColors.primary
                : hasValue
                    ? AppColors.primary.withValues(alpha: 0.3)
                    : AppColors.borderColor,
            width: isFocused ? 2 : 1,
          ),
          boxShadow: isFocused
              ? [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.02),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        child: Center(
          child: Text(
            displayText,
            style: TextStyle(
              fontSize: 28.sp, // 增加字体大小
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建重新发送部分
  Widget _buildResendSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 左侧占位，保持布局平衡
        const SizedBox(width: 1),
        // 右侧倒计时和重新发送
        Row(
          children: [
            Text(
              _canResend ? '没有收到验证码？' : '${_countdown}s 后可重新发送',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
            if (_canResend) ...[
              SizedBox(width: 4.w),
              GestureDetector(
                onTap: _resendCode,
                child: Text(
                  '重新发送',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

}


