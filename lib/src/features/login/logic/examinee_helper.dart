
import 'package:flutter_kit/src/core/environment.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';

///
/// 账号控制类
/// isLogin 是否登录
/// 登录
/// 退出登录
/// 刷新用户信息
class ExamineeHelper {
  static const String _loginStatusKey = 'examinee_login_status';

  final Storage _storage = locator<Storage>();

  /// 保存考生token
  Future<bool> saveJobSeekerToken(String token) async {
    try {
      final success = await _storage.writeString(key: Environment.JOB_SEEKER_TOKEN, value: token);
      if (success) {
        // 同时更新登录状态
        await _storage.writeBool(key: _loginStatusKey, value: true);
      }
      return success;
    } catch (e) {
      return false;
    }
  }

  /// 获取考生token
  Future<String?> getJobSeekerToken() async {
    try {
      return await _storage.read<String>(key: Environment.JOB_SEEKER_TOKEN);
    } catch (e) {
      return null;
    }
  }

  /// 检查是否已登录
  Future<bool> isLogin() async {
    try {
      final isLoggedIn = await _storage.read<bool>(key: _loginStatusKey);
      final token = await _storage.read<String>(key: Environment.JOB_SEEKER_TOKEN);

      // 只有当登录状态为true且token存在时才认为已登录
      return (isLoggedIn == true) && (token != null && token.isNotEmpty);
    } catch (e) {
      return false;
    }
  }

  /// 保存用户信息
  Future<bool> saveUserInfo(Map<String, dynamic> userInfo) async {
    try {
      return await _storage.write(key: Environment.JOB_SEEKER_USER_ACCOUNT, value: userInfo);
    } catch (e) {
      return false;
    }
  }

  /// 获取用户信息
  Future<Map<String, dynamic>?> getUserInfo() async {
    try {
      return await _storage.read<Map<String, dynamic>>(key: Environment.JOB_SEEKER_USER_ACCOUNT);
    } catch (e) {
      return null;
    }
  }

  /// 登录
  Future<bool> login(String token, {Map<String, dynamic>? userInfo}) async {
    try {
      // 保存token
      final tokenSaved = await saveJobSeekerToken(token);

      // 如果提供了用户信息，也保存用户信息
      bool userInfoSaved = true;
      if (userInfo != null) {
        userInfoSaved = await saveUserInfo(userInfo);
      }

      return tokenSaved && userInfoSaved;
    } catch (e) {
      return false;
    }
  }

  /// 退出登录
  Future<bool> logout() async {
    try {
      // 清除token
      await _storage.remove(key: Environment.JOB_SEEKER_TOKEN);
      // 清除用户信息
      await _storage.remove(key: Environment.JOB_SEEKER_USER_ACCOUNT);
      // 更新登录状态
      await _storage.writeBool(key: _loginStatusKey, value: false);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 刷新用户信息
  Future<bool> refreshUserInfo(Map<String, dynamic> newUserInfo) async {
    try {
      // 只有在已登录状态下才允许刷新用户信息
      if (await isLogin()) {
        return await saveUserInfo(newUserInfo);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 清除所有数据
  Future<void> clearAllData() async {
    try {
      await _storage.remove(key: Environment.JOB_SEEKER_TOKEN);
      await _storage.remove(key: Environment.JOB_SEEKER_USER_ACCOUNT);
      await _storage.remove(key: _loginStatusKey);
    } catch (e) {
      // 忽略错误，确保清除操作不会失败
    }
  }
}
