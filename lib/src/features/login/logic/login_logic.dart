import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/repositories/login_repository.dart';

/// 登录页面业务逻辑
class LoginLogic extends ViewStateLogic {
  final LoginRepository repository;

  LoginLogic({
    required this.repository,
  });

  @override
  void loadData() {}

  Future<UnifiedResponse<String>> login(
      String phone, String password, String ident, String vercode) async {
    return await repository.ExamineeLogin(
        phone: phone, password: password, ident: ident, vercode: vercode);
  }

  //获取图形验证码
  Future<UnifiedResponse<String>> getImage(
      String width, String height, int type) async {
    return await repository.getImage(width: width, height: height, type: type);
  }
}
