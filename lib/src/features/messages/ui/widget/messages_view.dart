import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';

/// 聊天消息数据模型
class ChatMessage {
  final String avatarUrl;
  final String name;
  final String lastMessage;
  final String time;
  final int unreadCount;
  final bool isRead;

  ChatMessage({
    required this.avatarUrl,
    required this.name,
    required this.lastMessage,
    required this.time,
    this.unreadCount = 0,
  }) : isRead = unreadCount == 0;
}

/// 消息页面主视图
class MessagesPageView extends StatefulWidget {
  const MessagesPageView({super.key});

  @override
  State<MessagesPageView> createState() => _MessagesPageViewState();
}

class _MessagesPageViewState extends State<MessagesPageView> {
  int _selectedTabIndex = 0; // 0: 全部, 1: 未读, 2: 已读

  // 模拟的聊天数据
  final List<ChatMessage> _allMessages = [
    ChatMessage(
      avatarUrl: 'https://placehold.co/100x100/FFE3E7/FF7185?text=HR',
      name: '某科技公司HR-张女士',
      lastMessage: '您好，我们觉得您的简历非常匹配我们的职位要求...',
      time: '14:20',
      unreadCount: 2,
    ),
    ChatMessage(
      avatarUrl: 'https://placehold.co/100x100/E0E7FF/3B82F6?text=李',
      name: '李先生-猎头顾问',
      lastMessage: '好的，我们保持联系，有合适的机会会第一时间联系您',
      time: '11:35',
    ),
    ChatMessage(
      avatarUrl: 'https://placehold.co/100x100/D1FAE5/10B981?text=王',
      name: '互联网公司-王经理',
      lastMessage: '感谢您的回复！我们会尽快安排面试时间',
      time: '昨天',
    ),
    ChatMessage(
      avatarUrl: 'https://placehold.co/100x100/FEF3C7/F59E0B?text=周',
      name: '周女士-HR专员',
      lastMessage: '收到，谢谢！期待与您的进一步沟通',
      time: '07-20',
      unreadCount: 1,
    ),
    ChatMessage(
      avatarUrl: 'https://placehold.co/100x100/FECACA/EF4444?text=陈',
      name: '陈总-创业公司',
      lastMessage: '我们对您的背景很感兴趣，方便电话沟通吗？',
      time: '07-19',
      unreadCount: 3,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      body: Stack(
        children: [
          // 背景渐变 - 延伸到状态栏
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 200.h + MediaQuery.of(context).padding.top,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.primary.withValues(alpha: 0.08),
                    AppColors.primary.withValues(alpha: 0.04),
                    AppColors.primary.withValues(alpha: 0.02),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // 主要内容
          SafeArea(
            child: Column(
              children: [
                _buildMinimalHeader(),
                _buildSegmentedControl(),
                Expanded(child: _buildCurrentTab()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建极简头部
  Widget _buildMinimalHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 24.h),
      color: Colors.transparent,
      child: Row(
        children: [
          Text(
            '消息',
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
              letterSpacing: -0.8,
            ),
          ),
          const Spacer(),
          // 极简搜索图标
          GestureDetector(
            onTap: () {
              // TODO: 处理搜索点击
            },
            child: Container(
              width: 44.w,
              height: 44.w,
              decoration: BoxDecoration(
                color: AppColors.cardBackground.withValues(alpha: 0.9),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.search_outlined,
                size: 22.w,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建极简分段控件
  Widget _buildSegmentedControl() {
    return Container(
      padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 16.h),
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: AppColors.pageBackground,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Row(
          children: [
            _buildTabItem(index: 0, text: '全部'),
            _buildTabItem(index: 1, text: '未读'),
            _buildTabItem(index: 2, text: '已读'),
          ],
        ),
      ),
    );
  }

  /// 构建极简Tab项
  Widget _buildTabItem({required int index, required String text}) {
    final bool isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTabIndex = index;
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: EdgeInsets.symmetric(vertical: 12.h),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.cardBackground : Colors.transparent,
            borderRadius: BorderRadius.circular(13.r),
          ),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 15.sp,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected ? AppColors.textPrimary : AppColors.textSecondary,
            ),
          ),
        ),
      ),
    );
  }

  /// 根据当前选择的Tab构建对应的内容
  Widget _buildCurrentTab() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildAllMessagesTab();
      case 1:
        return _buildFilteredMessagesTab(isRead: false);
      case 2:
        return _buildFilteredMessagesTab(isRead: true);
      default:
        return Container();
    }
  }

  /// 构建"全部"Tab的内容
  Widget _buildAllMessagesTab() {
    return Container(
      color: AppColors.pageBackground,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // 极简的功能入口
          Container(
            margin: EdgeInsets.fromLTRB(24.w, 8.h, 24.w, 16.h),
            child: Row(
              children: [
                _buildQuickAction(
                  icon: Icons.work_outline,
                  label: '面试邀约',
                  count: '2',
                  onTap: () {
                    // TODO: 处理面试邀约点击
                  },
                ),
                SizedBox(width: 12.w),
                _buildQuickAction(
                  icon: Icons.visibility_outlined,
                  label: '谁看过我',
                  onTap: () {
                    // TODO: 处理谁看过我点击
                  },
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 8.h,
            color: AppColors.pageBackground,
          ),
          // 消息列表
          ..._allMessages.map((msg) => ChatListItem(
            message: msg,
            onTap: () {
              // TODO: 处理聊天项点击
            },
          )),
        ],
      ),
    );
  }

  /// 构建快捷操作按钮
  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    String? count,
    VoidCallback? onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: AppColors.borderColor.withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20.w,
                color: AppColors.textSecondary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              if (count != null) ...[
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    count,
                    style: TextStyle(
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建"未读"和"已读"Tab的内容
  Widget _buildFilteredMessagesTab({required bool isRead}) {
    final filteredMessages = _allMessages.where((m) => m.isRead == isRead).toList();

    if (filteredMessages.isEmpty) {
      return Container(
        color: AppColors.pageBackground,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80.w,
                height: 80.w,
                decoration: BoxDecoration(
                  color: AppColors.pageBackground,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isRead ? Icons.mark_email_read_outlined : Icons.mark_email_unread_outlined,
                  size: 32.w,
                  color: AppColors.textSecondary.withValues(alpha: 0.5),
                ),
              ),
              SizedBox(height: 24.h),
              Text(
                isRead ? '暂无已读消息' : '暂无未读消息',
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                isRead ? '已读消息会在这里显示' : '新消息会在这里显示',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 15.sp,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: AppColors.pageBackground,
      child: ListView(
        padding: EdgeInsets.only(top: 16.h),
        children: filteredMessages.map((msg) => ChatListItem(
          message: msg,
          onTap: () {
            // TODO: 处理聊天项点击
          },
        )).toList(),
      ),
    );
  }
}



/// 聊天列表项组件 - 重新设计更专业美观
class ChatListItem extends StatelessWidget {
  final ChatMessage message;
  final VoidCallback? onTap;

  const ChatListItem({
    super.key,
    required this.message,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 1.h),
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          border: Border(
            bottom: BorderSide(
              color: AppColors.borderColor.withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头像区域
            _buildAvatar(),
            SizedBox(width: 14.w),
            // 内容区域
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  SizedBox(height: 6.h),
                  _buildMessage(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar() {
    return Stack(
      children: [
        Container(
          width: 52.w,
          height: 52.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: NetworkImage(message.avatarUrl),
              fit: BoxFit.cover,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        // 在线状态指示器
        if (!message.isRead)
          Positioned(
            right: 2.w,
            top: 2.w,
            child: Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.cardBackground,
                  width: 2,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// 构建头部信息（姓名和时间）
  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Text(
            message.name,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
              height: 1.2,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          message.time,
          style: TextStyle(
            fontSize: 13.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建消息内容
  Widget _buildMessage() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(
          child: Text(
            message.lastMessage,
            style: TextStyle(
              fontSize: 15.sp,
              color: message.isRead
                  ? AppColors.textSecondary
                  : AppColors.textPrimary.withValues(alpha: 0.8),
              height: 1.4,
              fontWeight: message.isRead ? FontWeight.w400 : FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (message.unreadCount > 0) ...[
          SizedBox(width: 12.w),
          _buildUnreadBadge(),
        ],
      ],
    );
  }

  /// 构建未读消息徽章
  Widget _buildUnreadBadge() {
    return Container(
      constraints: BoxConstraints(minWidth: 22.w),
      height: 22.w,
      padding: EdgeInsets.symmetric(horizontal: 6.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(11.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          message.unreadCount > 99 ? '99+' : message.unreadCount.toString(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 11.sp,
            fontWeight: FontWeight.w700,
            height: 1,
          ),
        ),
      ),
    );
  }
}
