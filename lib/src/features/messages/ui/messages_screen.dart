import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/widgets/view_state_widget.dart';
import 'package:flutter_kit/src/features/messages/logic/messages_logic.dart';
import 'package:flutter_kit/src/features/messages/ui/widget/messages_view.dart';
import 'package:flutter_kit/src/shared/locator.dart';

@RoutePage()
class MessagesScreen extends ViewStateWidget<MessagesLogic> {
  const MessagesScreen({super.key});

  @override
  MessagesLogic createController() {
    return locator<MessagesLogic>();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, MessagesLogic logic) {
    return null; // 不使用默认AppBar，在MessagesPageView中自定义
  }

  @override
  Widget buildBody(BuildContext context, MessagesLogic logic) {
    // 使用新的MessagesPageView替代原来的实现
    return const MessagesPageView();
  }

  @override
  bool useScaffold() => false; // 不使用外层Scaffold，让MessagesPageView自己处理


}
