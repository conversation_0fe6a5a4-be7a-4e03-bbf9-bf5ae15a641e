import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';

/// 用户信息数据模型
class UserProfile {
  final String name;
  final String avatar;
  final String email;
  final String phone;
  final String jobTitle;
  final String company;
  final int resumeViews;
  final int jobApplications;

  UserProfile({
    required this.name,
    required this.avatar,
    required this.email,
    required this.phone,
    required this.jobTitle,
    required this.company,
    required this.resumeViews,
    required this.jobApplications,
  });
}

/// 菜单项数据模型
class ProfileMenuItem {
  final String title;
  final String icon;
  final String? subtitle;
  final bool showArrow;
  final VoidCallback? onTap;

  ProfileMenuItem({
    required this.title,
    required this.icon,
    this.subtitle,
    this.showArrow = true,
    this.onTap,
  });
}

/// 我的页面逻辑
class ProfileLogic extends ViewStateLogic {
  UserProfile? _userProfile;
  List<ProfileMenuItem> _menuItems = [];
  
  UserProfile? get userProfile => _userProfile;
  List<ProfileMenuItem> get menuItems => _menuItems;
  
  @override
  void loadData() {
    setLoading();
    
    // 模拟网络请求延迟
    Future.delayed(const Duration(milliseconds: 600), () {
      _userProfile = UserProfile(
        name: '张三',
        avatar: '👤',
        email: '<EMAIL>',
        phone: '138****8888',
        jobTitle: 'Flutter开发工程师',
        company: '某某科技有限公司',
        resumeViews: 128,
        jobApplications: 15,
      );
      
      _menuItems = [
        ProfileMenuItem(
          title: '我的简历',
          icon: '📄',
          subtitle: '完善简历，获得更多机会',
        ),
        ProfileMenuItem(
          title: '求职意向',
          icon: '🎯',
          subtitle: '设置期望职位和薪资',
        ),
        ProfileMenuItem(
          title: '投递记录',
          icon: '📝',
          subtitle: '查看投递历史',
        ),
        ProfileMenuItem(
          title: '收藏职位',
          icon: '❤️',
          subtitle: '管理收藏的职位',
        ),
        ProfileMenuItem(
          title: '面试邀请',
          icon: '📅',
          subtitle: '查看面试安排',
        ),
        ProfileMenuItem(
          title: '设置',
          icon: '⚙️',
          subtitle: '账号与隐私设置',
        ),
        ProfileMenuItem(
          title: '帮助与反馈',
          icon: '❓',
          subtitle: '常见问题和意见反馈',
        ),
        ProfileMenuItem(
          title: '关于我们',
          icon: 'ℹ️',
          subtitle: '了解更多信息',
        ),
      ];
      
      setSuccess(_userProfile);
    });
  }
  
  /// 刷新数据
  void refreshData() {
    loadData();
  }
  
  /// 退出登录
  void logout() {
    // TODO: 实现退出登录逻辑
  }
}
