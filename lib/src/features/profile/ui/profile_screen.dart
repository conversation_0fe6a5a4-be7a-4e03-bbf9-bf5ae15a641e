import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/widgets/view_state_widget.dart';
import 'package:flutter_kit/src/features/profile/logic/profile_logic.dart';
import 'package:flutter_kit/src/features/profile/ui/widget/profile_view.dart';
import 'package:flutter_kit/src/shared/locator.dart';

@RoutePage()
class ProfileScreen extends ViewStateWidget<ProfileLogic> {
  const ProfileScreen({super.key});

  @override
  ProfileLogic createController() {
    return locator<ProfileLogic>();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, ProfileLogic logic) {
    return null; // 不使用默认AppBar，在ProfilePageView中自定义
  }

  @override
  Widget buildBody(BuildContext context, ProfileLogic logic) {
    // 使用新的ProfilePageView替代原来的实现
    return const ProfilePageView();
  }

  @override
  bool useScaffold() => false; // 不使用外层Scaffold，让ProfilePageView自己处理


}
