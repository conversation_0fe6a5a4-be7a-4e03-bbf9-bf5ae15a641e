import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 发现页面主视图
class DiscoveryPageView extends StatefulWidget {
  const DiscoveryPageView({super.key});

  @override
  State<DiscoveryPageView> createState() => _DiscoveryPageViewState();
}

class _DiscoveryPageViewState extends State<DiscoveryPageView> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      body: Stack(
        children: [
          // 背景渐变 - 延伸到状态栏
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 200.h + MediaQuery.of(context).padding.top,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.primary.withValues(alpha: 0.08),
                    AppColors.primary.withValues(alpha: 0.04),
                    AppColors.primary.withValues(alpha: 0.02),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // 主要内容
          SafeArea(
            child: Column(
              children: [
                _buildModernHeader(),
                Expanded(
                  child: Container(
                    color: AppColors.pageBackground,
                    child: TabBarView(
                      controller: _tabController,
                      children: const [
                        LiveStreamTab(),
                        JobFairTab(),
                        ComingSoonTab(), // 演示第三个tab
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 现代化头部设计
  Widget _buildModernHeader() {
    return Container(
      color: Colors.transparent,
      child: _buildModernTabBar(),
    );
  }



  /// 现代化Tab栏
  Widget _buildModernTabBar() {
    return Container(
      padding: EdgeInsets.fromLTRB(20.w, 12.h, 20.w, 0),
      child: Column(
        children: [
          // Tab栏容器
          SizedBox(
            height: 32.h,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              indicator: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(16.r),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
              labelStyle: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppColors.textSecondary,
              dividerColor: Colors.transparent,
              labelPadding: EdgeInsets.symmetric(horizontal: 10.w),
              tabs: const [
                Tab(text: '直播'),
                Tab(text: '招聘会'),
                Tab(text: '企业'),
              ],
            ),
          ),
          // 底部分割线
          Container(
            height: 0.5.h,
            margin: EdgeInsets.only(top: 12.h),
            color: AppColors.borderColor,
          ),
        ],
      ),
    );
  }
}

/// 直播页面Tab
class LiveStreamTab extends StatelessWidget {
  const LiveStreamTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: StaggeredGrid.count(
        crossAxisCount: 2,
        mainAxisSpacing: 16.w,
        crossAxisSpacing: 16.w,
        children: const [
          LiveStreamCard(
            imageUrl: 'https://placehold.co/400x300/E5E7EB/A0AEC0?text=Tech',
            title: '前端开发岗直通车：资深HR在线答疑',
            time: '今天 19:30',
            status: LiveStatus.live,
            viewers: '1.8k',
          ),
          LiveStreamCard(
            imageUrl: 'https://placehold.co/400x500/E5E7EB/A0AEC0?text=Design',
            title: '产品经理职业规划：从0到1做产品',
            time: '明天 20:00',
            status: LiveStatus.upcoming,
          ),
          LiveStreamCard(
            imageUrl: 'https://placehold.co/400x500/E5E7EB/A0AEC0?text=AI',
            title: 'AI算法工程师前景与职业新机遇',
            time: '07-20 14:00',
            status: LiveStatus.replay,
          ),
          LiveStreamCard(
            imageUrl: 'https://placehold.co/400x300/E5E7EB/A0AEC0?text=CV',
            title: '决胜秋招-简历篇：让HR眼前一亮',
            time: '07-28 19:00',
            status: LiveStatus.upcoming,
          ),
        ],
      ),
    );
  }
}

/// 招聘会页面Tab
class JobFairTab extends StatelessWidget {
  const JobFairTab({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.all(16.w),
      children: [
        const JobFairCard(
          imageUrl: 'https://placehold.co/600x250/FFDDE1/FF7185?text=互联网科技专场',
          title: '互联网科技企业专场招聘会',
          date: '2025年8月15日',
          location: '国家会展中心 (上海)',
          isHot: true,
        ),
        SizedBox(height: 16.h),
        const JobFairCard(
          imageUrl: 'https://placehold.co/600x250/F3F4F6/6B7280?text=高校毕业生专场',
          title: '"逐梦青春"高校毕业生招聘会',
          date: '2025年9月1日 - 2日',
          location: '城市体育馆',
          isHot: false,
        ),
      ],
    );
  }
}

/// 直播状态枚举
enum LiveStatus { live, upcoming, replay }

/// 直播卡片组件
class LiveStreamCard extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String time;
  final LiveStatus status;
  final String? viewers;

  const LiveStreamCard({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.time,
    required this.status,
    this.viewers,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 16,
              offset: const Offset(0, 4),
            )
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageWithStatus(),
            Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        time,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      _buildActionButton(),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildImageWithStatus() {
    return AspectRatio(
      aspectRatio: 4 / 3,
      child: Stack(
        children: [
          Positioned.fill(
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, progress) =>
                  progress == null ? child : Container(color: AppColors.borderColor),
              errorBuilder: (context, error, stackTrace) =>
                  Container(color: AppColors.borderColor),
            ),
          ),
          if (status == LiveStatus.live)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.black.withValues(alpha: 0.4), Colors.transparent],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                  ),
                ),
              ),
            ),
          Positioned(top: 8.w, left: 8.w, child: _buildStatusTag()),
          if (status == LiveStatus.live && viewers != null)
            Positioned(
              bottom: 8.w,
              left: 8.w,
              child: Row(
                children: [
                  Icon(PhosphorIcons.userCircle(), color: Colors.white, size: 14.w),
                  SizedBox(width: 4.w),
                  Text(
                    '$viewers 人在线',
                    style: TextStyle(
                      color: Colors.white, 
                      fontSize: 12.sp, 
                      fontWeight: FontWeight.w500
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusTag() {
    switch (status) {
      case LiveStatus.live:
        return StatusTag(
          text: 'LIVE',
          icon: PhosphorIcons.broadcast(PhosphorIconsStyle.fill),
          backgroundColor: AppColors.primary,
          textColor: Colors.white,
        );
      case LiveStatus.upcoming:
        return StatusTag(
          text: '预告',
          icon: PhosphorIcons.clock(PhosphorIconsStyle.fill),
          backgroundColor: Colors.white.withValues(alpha: 0.8),
          textColor: AppColors.textPrimary,
        );
      case LiveStatus.replay:
        return StatusTag(
          text: '回放',
          icon: PhosphorIcons.arrowCounterClockwise(PhosphorIconsStyle.fill),
          backgroundColor: Colors.white.withValues(alpha: 0.8),
          textColor: AppColors.textPrimary,
        );
    }
  }

  Widget _buildActionButton() {
    switch (status) {
      case LiveStatus.live:
        return ActionButton(
          text: '观看',
          color: AppColors.primary,
          textColor: Colors.white
        );
      case LiveStatus.upcoming:
        return ActionButton(
          text: '预约',
          color: AppColors.primaryLight,
          textColor: AppColors.primary
        );
      case LiveStatus.replay:
        return ActionButton(
          text: '查看',
          color: AppColors.borderColor,
          textColor: AppColors.textSecondary
        );
    }
  }
}

/// 招聘会卡片组件
class JobFairCard extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String date;
  final String location;
  final bool isHot;

  const JobFairCard({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.date,
    required this.location,
    required this.isHot,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 16,
              offset: const Offset(0, 4),
            )
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.network(
              imageUrl,
              height: 128.h,
              width: double.infinity,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, progress) =>
                  progress == null ? child : Container(color: AppColors.borderColor, height: 128.h),
              errorBuilder: (context, error, stackTrace) =>
                  Container(color: AppColors.borderColor, height: 128.h),
            ),
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHotTag(),
                  SizedBox(height: 8.h),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildInfoRow(PhosphorIcons.calendarBlank(), date),
                  SizedBox(height: 8.h),
                  _buildInfoRow(PhosphorIcons.mapPin(), location),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildHotTag() {
    if (isHot) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
        decoration: BoxDecoration(
          gradient: const LinearGradient(colors: [AppColors.primary, Color(0xFFF85A6F)]),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Text(
          '火热报名中',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12.sp,
            fontWeight: FontWeight.bold
          )
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: AppColors.borderColor,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Text(
          '即将开始',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12.sp,
            fontWeight: FontWeight.bold
          )
        ),
      );
    }
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primary, size: 16.w),
        SizedBox(width: 8.w),
        Text(
          text,
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}

/// 状态标签组件
class StatusTag extends StatelessWidget {
  final String text;
  final IconData icon;
  final Color backgroundColor;
  final Color textColor;

  const StatusTag({
    super.key,
    required this.text,
    required this.icon,
    required this.backgroundColor,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: textColor, size: 14.w),
          SizedBox(width: 4.w),
          Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: 12.sp,
              fontWeight: FontWeight.bold
            )
          ),
        ],
      ),
    );
  }
}

/// 行为按钮组件
class ActionButton extends StatelessWidget {
  final String text;
  final Color color;
  final Color textColor;

  const ActionButton({
    super.key,
    required this.text,
    required this.color,
    required this.textColor
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12.sp,
          fontWeight: FontWeight.w600
        )
      ),
    );
  }
}

/// 即将推出Tab（演示用）
class ComingSoonTab extends StatelessWidget {
  const ComingSoonTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.pageBackground,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_outlined,
              size: 64.w,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              '企业专区',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '即将上线，敬请期待',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
