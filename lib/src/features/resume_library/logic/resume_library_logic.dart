import 'package:flutter/material.dart';

// 简历数据模型
class ResumeItem {
  final String id;
  final String title;
  final bool isDefault;
  final double completeness; // 完善度 0.0-1.0
  final String updatedDate;
  final String jobTitle;
  final String salary;
  final String city;

  ResumeItem({
    required this.id,
    required this.title,
    required this.isDefault,
    required this.completeness,
    required this.updatedDate,
    required this.jobTitle,
    required this.salary,
    required this.city,
  });
}

class ResumeLibraryLogic extends ChangeNotifier {
  // 简历列表
  List<ResumeItem> _resumes = [];
  List<ResumeItem> get resumes => _resumes;

  // 加载状态
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // 统计数据
  int get totalCount => _resumes.length;

  /// 初始化数据
  void initData() {
    _loadResumeData();
  }

  /// 加载简历数据
  void _loadResumeData() {
    // 模拟简历数据
    _resumes = [
      ResumeItem(
        id: '1',
        title: 'UI设计师求职简历',
        isDefault: true,
        completeness: 0.95,
        updatedDate: '07-24',
        jobTitle: 'UI设计师',
        salary: '12K-20K',
        city: '汕头市',
      ),
      ResumeItem(
        id: '2',
        title: '1112',
        isDefault: false,
        completeness: 0.30,
        updatedDate: '07-20',
        jobTitle: '前端开发',
        salary: '15K-25K',
        city: '揭阳市',
      ),
      ResumeItem(
        id: '3',
        title: '保安求职简历',
        isDefault: false,
        completeness: 0.65,
        updatedDate: '07-15',
        jobTitle: '保安',
        salary: '3000-5000',
        city: '汕头市',
      ),
      ResumeItem(
        id: '4',
        title: '保安求职简历',
        isDefault: false,
        completeness: 1.0,
        updatedDate: '07-15',
        jobTitle: '保安',
        salary: '6000-15000',
        city: '汕头市',
      ),
    ];
    notifyListeners();
  }

  /// 刷新数据
  Future<void> refreshData() async {
    _isLoading = true;
    notifyListeners();

    try {
      // TODO: 实际的网络请求
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求
      _loadResumeData();
    } catch (e) {
      // 处理错误
      debugPrint('刷新数据失败：$e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 添加新简历
  Future<void> addNewResume() async {
    try {
      // TODO: 实现添加新简历的逻辑
      debugPrint('添加新简历');
    } catch (e) {
      debugPrint('添加简历失败：$e');
    }
  }

  /// 编辑简历
  Future<void> editResume(String resumeId) async {
    try {
      // TODO: 实现编辑简历的逻辑
      debugPrint('编辑简历：$resumeId');
    } catch (e) {
      debugPrint('编辑简历失败：$e');
    }
  }

  /// 预览简历
  Future<void> previewResume(String resumeId) async {
    try {
      // TODO: 实现预览简历的逻辑
      debugPrint('预览简历：$resumeId');
    } catch (e) {
      debugPrint('预览简历失败：$e');
    }
  }

  /// 复制简历
  Future<void> copyResume(String resumeId) async {
    try {
      // TODO: 实现复制简历的逻辑
      final originalResume = _resumes.firstWhere((r) => r.id == resumeId);
      final newResume = ResumeItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '${originalResume.title} - 副本',
        isDefault: false,
        completeness: originalResume.completeness,
        updatedDate: _getCurrentDate(),
        jobTitle: originalResume.jobTitle,
        salary: originalResume.salary,
        city: originalResume.city,
      );
      
      _resumes.add(newResume);
      notifyListeners();
      debugPrint('复制简历成功');
    } catch (e) {
      debugPrint('复制简历失败：$e');
    }
  }

  /// 删除简历
  Future<void> deleteResume(String resumeId) async {
    try {
      // TODO: 实现删除简历的逻辑
      _resumes.removeWhere((r) => r.id == resumeId);
      notifyListeners();
      debugPrint('删除简历成功');
    } catch (e) {
      debugPrint('删除简历失败：$e');
    }
  }

  /// 设置默认简历
  Future<void> setDefaultResume(String resumeId) async {
    try {
      // 先取消所有默认状态
      for (int i = 0; i < _resumes.length; i++) {
        _resumes[i] = ResumeItem(
          id: _resumes[i].id,
          title: _resumes[i].title,
          isDefault: _resumes[i].id == resumeId,
          completeness: _resumes[i].completeness,
          updatedDate: _resumes[i].updatedDate,
          jobTitle: _resumes[i].jobTitle,
          salary: _resumes[i].salary,
          city: _resumes[i].city,
        );
      }
      notifyListeners();
      debugPrint('设置默认简历成功');
    } catch (e) {
      debugPrint('设置默认简历失败：$e');
    }
  }



  /// 获取当前日期
  String _getCurrentDate() {
    final now = DateTime.now();
    return '${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  /// 根据ID获取简历
  ResumeItem? getResumeById(String id) {
    try {
      return _resumes.firstWhere((r) => r.id == id);
    } catch (e) {
      return null;
    }
  }




}
