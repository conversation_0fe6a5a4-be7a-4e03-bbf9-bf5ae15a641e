import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import '../logic/resume_library_logic.dart';
import 'widget/resume_library_view.dart';

@RoutePage()
class ResumeLibraryScreen extends StatefulWidget {
  const ResumeLibraryScreen({super.key});

  @override
  State<ResumeLibraryScreen> createState() => _ResumeLibraryScreenState();
}

class _ResumeLibraryScreenState extends State<ResumeLibraryScreen> {
  late final ResumeLibraryLogic _logic;

  @override
  void initState() {
    super.initState();
    _logic = ResumeLibraryLogic();
  }

  @override
  void dispose() {
    _logic.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResumeLibraryView(logic: _logic);
  }
}
