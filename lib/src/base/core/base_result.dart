import '../../../generated/json/base/json_convert_content.dart';

/// 基础结果类型
/// 参考joke_fun项目的设计，简化API调用结果处理
/// 统一的BaseResult类，兼容新旧API格式
class BaseResult<T> {
  T? data;
  dynamic code; // 支持int和String类型
  String? msg;

  BaseResult({this.data, this.code, this.msg});

  /// 兼容新格式的构造函数 (data字段)
  BaseResult.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null && json['data'] != 'null') {
      data = json['data'];
    }
    code = json['code'];
    msg = json['msg'];
  }

  /// 兼容旧格式的构造函数 (body字段)
  BaseResult.fromJsonLegacy(Map<String, dynamic> json) {
    if (json['body'] != null && json['body'] != 'null') {
      try {
        data = JsonConvert.fromJsonAsT<T>(json['body']);
      } catch (e) {
        // fix List<String>
        if (json['body'] is List) {
          data = List<String>.from(json['body']) as T;
        }
      }
    }
    code = json['code'];
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data;
    }
    data['code'] = this.code;
    data['msg'] = this.msg;
    return data;
  }

  /// 判断是否成功 - 兼容新旧格式
  bool isSuccess() {
    if (code is int) {
      return code == 200;
    } else if (code is String) {
      return code == '0';
    }
    return false;
  }

  bool isEmpty() {
    if (data == null) {
      return true;
    } else {
      if (data is List) {
        return (data as List).isEmpty;
      }
      return false;
    }
  }

  bool noMoreData(int pageSize) {
    if (data == null) {
      return true;
    } else {
      if (data is List) {
        return (data as List).length < pageSize;
      }
      return false;
    }
  }
}
