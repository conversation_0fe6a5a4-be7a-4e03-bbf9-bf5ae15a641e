/// 视图状态基类
/// 参考joke_fun项目的优秀设计，使用继承方式而非枚举
class ViewState<T> {
  T? data;
  int? errorCode;
  String? errorMessage;

  ViewState({this.data, this.errorCode, this.errorMessage});

  bool isLoading() => this is ViewStateLoading;
  bool isError() => this is ViewStateError;
  bool isEmpty() => this is ViewStateEmpty;
  bool isSuccess() => this is ViewStateSuccess;
  bool isFail() => this is ViewStateFail;
  bool isRefreshing() => this is ViewStateRefreshing;
  bool isLoadingMore() => this is ViewStateLoadingMore;

  /// 是否正在执行某种加载操作
  bool isAnyLoading() => isLoading() || isRefreshing() || isLoadingMore();
}

/// 加载中状态
class ViewStateLoading<T> extends ViewState<T> {}

/// 刷新中状态
class ViewStateRefreshing<T> extends ViewState<T> {
  ViewStateRefreshing(T? data) : super(data: data);
}

/// 加载更多中状态
class ViewStateLoadingMore<T> extends ViewState<T> {
  ViewStateLoadingMore(T? data) : super(data: data);
}

/// 空数据状态
class ViewStateEmpty<T> extends ViewState<T> {}

/// 网络错误状态
class ViewStateError<T> extends ViewState<T> {
  ViewStateError(int? errorCode, String? errorMessage)
      : super(errorCode: errorCode, errorMessage: errorMessage);
}

/// 业务失败状态
class ViewStateFail<T> extends ViewState<T> {
  ViewStateFail(int? errorCode, String? errorMessage)
      : super(errorCode: errorCode, errorMessage: errorMessage);
}

/// 成功状态
class ViewStateSuccess<T> extends ViewState<T> {
  ViewStateSuccess(T data) : super(data: data);
}
