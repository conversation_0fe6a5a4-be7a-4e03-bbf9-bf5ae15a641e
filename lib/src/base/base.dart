// Flutter Kit Base Architecture
//
// 提供统一的状态管理、UI组件和AppBar解决方案
//
// 使用示例:
// ```dart
// import 'package:flutter_kit/src/base/base.dart';
//
// class MyLogic extends ViewStateLogic {
//   MyData? _data;
//   MyData? get data => _data;
//
//   @override
//   void loadData() {
//     sendRequest(_fetchData(), successCallback: (data) {
//       _data = data;
//     });
//   }
//
//   Future<BaseResult<MyData>> _fetchData() async {
//     // 实现数据获取逻辑
//     return BaseResult(data: MyData(), code: 200, msg: 'success');
//   }
// }
//
// class MyPage extends ViewStateWidget<MyLogic> {
//   @override
//   MyLogic createController() => MyLogic();
//
//   @override
//   Widget buildBody(BuildContext context, MyLogic logic) {
//     return MyDataView(logic.data);
//   }
//
//   @override
//   PreferredSizeWidget? buildAppBar(BuildContext context, MyLogic logic) {
//     return SimpleAppBar.basic(title: 'My Page');
//   }
// }
// ```



// Core
export 'core/view_state.dart';
export 'core/base_result.dart';

// Logic
export 'logic/view_state_logic.dart';
export 'logic/view_state_paging_logic.dart';

// Widgets
export 'widgets/view_state_widget.dart';
export 'widgets/view_state_paging_widget.dart';
export 'widgets/state_widget_builder.dart';

// Localization
export 'l10n/base_localizations.dart';

// AppBars
export '../shared/components/app_bars/simple_app_bar.dart';
