import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';

import '../base.dart';

/// 分页逻辑基类
abstract class ViewStatePagingLogic extends ViewStateLogic {
  int curPage = 1;
  int pageSize = 10;
  
  /// 子类重写此方法来刷新分页数据
  void refreshPaging();
  
  /// 子类重写此方法来加载更多分页数据
  void loadMorePaging();
  
  /// 发送刷新分页请求
  void sendRefreshPagingRequest<T>(
    Future<UnifiedResponse<T>> sendRequestBlock, {
    bool bindViewState = true,
    ValueChanged<T>? successBlock,
    VoidCallback? emptyCallback,
    VoidCallback? failCallback,
  }) {
    sendRequestBlock.then((result) {
      result.when(
        success: (data, message) {
          curPage = 1;
          if (successBlock != null) {
            successBlock(data);
          }
        },
        failure: (message, code, error) {
          if (failCallback != null) {
            failCallback();
          }
        },
        empty: (message) {
          if (bindViewState) {
            viewState = ViewStateEmpty();
            notifyListeners();
          }
          if (emptyCallback != null) {
            emptyCallback();
          }
        },
      );
    }).catchError((e) {
      if (failCallback != null) {
        failCallback();
      }
      debugPrint("sendRefreshPagingRequest catchError: $e");
    });
  }
  
  /// 发送加载更多分页请求
  void sendLoadMorePagingRequest<T>(
    Future<UnifiedResponse<T>> sendRequestBlock, {
    bool bindViewState = true,
    bool Function(UnifiedResponse<T>)? judgeNoMoreDataBlock,
    ValueChanged<T>? successBlock,
    VoidCallback? emptyCallback,
    VoidCallback? failCallback,
  }) {
    sendRequestBlock.then((result) {
      result.when(
        success: (data, message) {
          curPage++;
          if (successBlock != null) {
            successBlock(data);
          }
        },
        failure: (message, code, error) {
          if (failCallback != null) {
            failCallback();
          }
        },
        empty: (message) {
          if (emptyCallback != null) {
            emptyCallback();
          }
        },
      );
    }).catchError((e) {
      if (failCallback != null) {
        failCallback();
      }
      debugPrint('sendLoadMorePagingRequest catchError: $e');
    });
  }
}
