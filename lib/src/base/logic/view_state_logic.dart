import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/base/core/view_state.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';

/// 视图状态逻辑基类
abstract class ViewStateLogic extends ChangeNotifier {
  ViewState _viewState = ViewState();
  
  ViewState get viewState => _viewState;

  /// 设置视图状态（供子类使用）
  @protected
  set viewState(ViewState state) {
    _viewState = state;
    notifyListeners();
  }

  /// 便捷的状态设置方法
  @protected
  void setLoading() {
    viewState = ViewStateLoading();
  }

  @protected
  void setSuccess<T>(T data) {
    viewState = ViewStateSuccess(data);
  }

  @protected
  void setError(String message, {int? code}) {
    viewState = ViewStateError(code ?? -1, message);
  }

  @protected
  void setEmpty() {
    viewState = ViewStateEmpty();
  }

  @protected
  void setRefreshing<T>(T? data) {
    viewState = ViewStateRefreshing(data);
  }

  @protected
  void setLoadingMore<T>(T? data) {
    viewState = ViewStateLoadingMore(data);
  }

  /// 子类重写此方法来加载数据
  void loadData() {}
  
  /// 发送请求的通用方法
  void sendRequest<T>(
    Future<UnifiedResponse<T>> sendRequestBlock, {
    bool bindViewState = true,
    bool showLoadingDialog = false,
    bool needLogin = false,
    bool emptyAsSuccess = false,
    bool Function(UnifiedResponse<T>)? judgeEmptyCallback,
    ValueChanged<T?>? successCallback,
    VoidCallback? emptyCallback,
    VoidCallback? failCallback,
  }) {
    if (bindViewState) {
      _viewState = ViewStateLoading();
      notifyListeners();
    }

    sendRequestBlock.then((result) {
      result.when(
        success: (data, message) {
          if (bindViewState) {
            _viewState = ViewStateSuccess(data);
            notifyListeners();
          }
          if (successCallback != null) {
            successCallback(data);
          }
        },
        failure: (message, code, error) {
          if (bindViewState) {
            _viewState = ViewStateFail(code, message);
            notifyListeners();
          }
          if (failCallback != null) {
            failCallback();
          }
        },
        empty: (message) {
          if (emptyAsSuccess) {
            if (bindViewState) {
              _viewState = ViewStateSuccess(null);
              notifyListeners();
            }
            if (successCallback != null) {
              successCallback(null);
            }
          } else {
            if (bindViewState) {
              _viewState = ViewStateEmpty();
              notifyListeners();
            }
            if (emptyCallback != null) {
              emptyCallback();
            }
          }
        },
      );
    }).catchError((e) {
      if (bindViewState) {
        _viewState = ViewStateError(-1, e.toString());
        notifyListeners();
      }
      if (failCallback != null) {
        failCallback();
      }
      debugPrint('sendRequest catchError: $e');
    });
  }
  
  /// 刷新数据
  void refresh() {
    loadData();
  }
  
  /// 重试
  void retry() {
    loadData();
  }
  
  /// 重置状态
  void reset() {
    _viewState = ViewState();
    notifyListeners();
  }
}
