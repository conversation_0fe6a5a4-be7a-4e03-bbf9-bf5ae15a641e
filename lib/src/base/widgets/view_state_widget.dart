import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/core/view_state.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/base/widgets/state_widget_builder.dart';
import 'package:provider/provider.dart';

/// 页面状态自动切换组件
abstract class ViewStateWidget<T extends ViewStateLogic> extends StatelessWidget {
  const ViewStateWidget({
    super.key,
    this.bindViewState = true,
    this.autoLoad = true,
  });

  final bool bindViewState;
  final bool autoLoad;

  /// 创建Controller实例，子类必须实现
  T createController();

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<T>(
      create: (_) {
        final controller = createController();
        if (autoLoad) {
          // 延迟执行，避免在build过程中调用
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.loadData();
          });
        }
        return controller;
      },
      child: Consumer<T>(
        builder: (context, logic, child) {
          Widget body;

          if (bindViewState) {
            final viewState = logic.viewState;

            Widget targetBody;
            if (viewState.isSuccess()) {
              targetBody = buildBody(context, logic);
            } else if (viewState.isError()) {
              targetBody = buildCustomErrorWidget() ??
                     StateWidgetBuilder.error(
                       context: context,
                       message: viewState.errorMessage ?? 'Unknown error',
                       onRetry: () => logic.retry(),
                     );
            } else if (viewState.isEmpty()) {
              targetBody = buildCustomEmptyWidget() ??
                     StateWidgetBuilder.empty(
                       context: context,
                       onRefresh: () => logic.refresh(),
                     );
            } else if (viewState.isFail()) {
              targetBody = buildCustomFailWidget() ??
                     StateWidgetBuilder.error(
                       context: context,
                       message: viewState.errorMessage ?? 'Request failed',
                       onRetry: () => logic.retry(),
                     );
            } else if (viewState.isLoading()) {
              // 检查是否有数据来决定显示哪种 loading
              if (hasData()) {
                // 有数据时，显示内容 + 顶部 loading
                targetBody = Stack(
                  children: [
                    buildBody(context, logic),
                    buildTopLoadingWidget(),
                  ],
                );
              } else {
                // 无数据时，显示全屏 loading
                targetBody = buildCustomLoadingWidget() ??
                       StateWidgetBuilder.loading(context: context);
              }
            } else {
              targetBody = buildCustomLoadingWidget() ??
                     StateWidgetBuilder.loading(context: context);
            }

            // 添加动画过渡
            body = AnimatedSwitcher(
              duration: getTransitionDuration(),
              transitionBuilder: getTransitionBuilder(),
              child: Container(
                key: ValueKey(viewState.runtimeType),
                color: Colors.transparent, // 确保容器背景透明
                child: targetBody,
              ),
            );
          } else {
            body = buildBody(context, logic);
          }

          return useScaffold()
              ? Scaffold(
                  appBar: buildAppBar(context, logic),
                  resizeToAvoidBottomInset: resizeToAvoidBottomInset(),
                  body: body,
                )
              : body;
        },
      ),
    );
  }

  /// 构建自定义加载Widget
  Widget? buildCustomLoadingWidget() => null;

  /// 构建自定义失败Widget
  Widget? buildCustomFailWidget() => null;

  /// 构建自定义空数据Widget
  Widget? buildCustomEmptyWidget() => null;

  /// 构建自定义错误Widget
  Widget? buildCustomErrorWidget() => null;

  /// 检查是否有数据（子类重写）
  bool hasData() => false;

  /// 获取状态切换动画时长
  Duration getTransitionDuration() => const Duration(milliseconds: 300);

  /// 获取状态切换动画构建器
  Widget Function(Widget, Animation<double>) getTransitionBuilder() {
    return _buildFadeSlideTransition;
  }

  /// 淡入淡出 + 轻微滑动动画（默认）
  static Widget _buildFadeSlideTransition(Widget child, Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.0, 0.1),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        )),
        child: child,
      ),
    );
  }

  /// 纯淡入淡出动画
  static Widget _buildFadeTransition(Widget child, Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  /// 缩放动画
  static Widget _buildScaleTransition(Widget child, Animation<double> animation) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeOutBack,
      )),
      child: FadeTransition(
        opacity: animation,
        child: child,
      ),
    );
  }

  /// 构建顶部加载Widget
  Widget buildTopLoadingWidget() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 3,
        child: LinearProgressIndicator(
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
        ),
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget? buildAppBar(BuildContext context, T logic) => null;

  /// 构建主体内容
  Widget buildBody(BuildContext context, T logic);

  /// 是否使用Scaffold
  bool useScaffold() => true;

  /// 是否调整避免底部输入法
  bool resizeToAvoidBottomInset() => true;

  /// 背景色
  Color? backgroundColor() => null;
}
