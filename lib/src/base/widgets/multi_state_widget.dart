import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../logic/view_state_logic.dart';
import '../core/view_state.dart';
import '../../shared/components/multi_state/multi_state_view.dart';

/// 多状态Widget基类
/// 提供基础的Provider和Logic管理，开发者自己决定在哪里使用MultiStateView
abstract class MultiStateWidget<T extends ViewStateLogic> extends StatelessWidget {
  const MultiStateWidget({
    super.key,
    this.autoLoad = true,
    this.useScaffold = true,
    this.resizeToAvoidBottomInset,
  });

  /// 是否自动加载数据
  final bool autoLoad;

  /// 是否使用Scaffold包装
  final bool useScaffold;

  /// 是否调整避免底部输入法
  final bool? resizeToAvoidBottomInset;

  /// 创建Controller实例，子类必须实现
  T createController();

  /// 构建页面内容，子类必须实现
  /// 在这里可以自由决定在哪里使用MultiStateView
  Widget buildBody(BuildContext context, T logic);

  /// 构建AppBar（可选）
  PreferredSizeWidget? buildAppBar(BuildContext context, T logic) => null;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<T>(
      create: (_) {
        final controller = createController();
        if (autoLoad) {
          // 延迟执行，避免在build过程中调用
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.loadData();
          });
        }
        return controller;
      },
      child: Consumer<T>(
        builder: (context, logic, child) {
          final body = buildBody(context, logic);

          return useScaffold
              ? Scaffold(
                  appBar: buildAppBar(context, logic),
                  resizeToAvoidBottomInset: resizeToAvoidBottomInset,
                  body: body,
                )
              : body;
        },
      ),
    );
  }
}

/// 扩展MultiStateController，添加便捷的状态更新方法
extension MultiStateControllerExtension on MultiStateController {
  /// 根据ViewState自动更新状态
  void updateFromViewState(ViewState viewState, {bool hasData = false}) {
    if (viewState.isLoading()) {
      showLoading();
    } else if (viewState.isSuccess()) {
      if (hasData) {
        showContent();
      } else {
        showEmpty();
      }
    } else if (viewState.isError()) {
      showError(viewState.errorMessage);
    } else if (viewState.isEmpty()) {
      showEmpty();
    } else if (viewState.isFail()) {
      showNetworkError();
    }
  }
}
