import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_paging_logic.dart';

/// 简单的分页列表组件
/// 参考joke_fun项目的优秀设计
class SimplePagingListView<T> extends StatefulWidget {
  const SimplePagingListView({
    super.key,
    required this.logic,
    required this.itemBuilder,
    this.separatorBuilder,
    this.enableRefresh = true,
    this.enableLoadMore = true,
    this.scrollController,
    this.physics,
    this.padding,
  });

  final ViewStatePagingLogic logic;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context, int index)? separatorBuilder;
  final bool enableRefresh;
  final bool enableLoadMore;
  final ScrollController? scrollController;
  final ScrollPhysics? physics;
  final EdgeInsetsGeometry? padding;

  @override
  State<SimplePagingListView<T>> createState() => _SimplePagingListViewState<T>();
}

class _SimplePagingListViewState<T> extends State<SimplePagingListView<T>> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();

    // 监听滚动，实现加载更多
    if (widget.enableLoadMore) {
      _scrollController.addListener(_onScroll);
    }

    // 监听状态变化
    widget.logic.addListener(_onStateChanged);
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    widget.logic.removeListener(_onStateChanged);
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      widget.logic.loadMorePaging();
    }
  }

  void _onStateChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    // 这里可以根据logic的状态来显示不同的UI
    // 为了简化，直接显示列表
    return _buildListView(context);
  }

  Widget _buildListView(BuildContext context) {
    // 获取数据列表（这里需要根据具体的logic实现来获取）
    final items = _getItems();

    Widget listView;

    if (widget.separatorBuilder != null) {
      listView = ListView.separated(
        controller: _scrollController,
        physics: widget.physics,
        padding: widget.padding,
        itemCount: items.length,
        itemBuilder: (context, index) {
          return widget.itemBuilder(context, items[index], index);
        },
        separatorBuilder: widget.separatorBuilder!,
      );
    } else {
      listView = ListView.builder(
        controller: _scrollController,
        physics: widget.physics,
        padding: widget.padding,
        itemCount: items.length,
        itemBuilder: (context, index) {
          return widget.itemBuilder(context, items[index], index);
        },
      );
    }

    if (widget.enableRefresh) {
      return RefreshIndicator(
        onRefresh: () async {
          widget.logic.refreshPaging();
        },
        child: listView,
      );
    }

    return listView;
  }

  List<T> _getItems() {
    // 通过反射或类型检查来获取具体logic的数据
    final logic = widget.logic;

    try {
      // 使用 noSuchMethod 来安全地访问动态属性
      final dynamic dynamicLogic = logic;

      // 尝试获取 jobs 属性
      try {
        final jobs = dynamicLogic.jobs;
        if (jobs != null && jobs is List) {
          return List<T>.from(jobs);
        }
      } catch (e) {
        // jobs 属性不存在，继续尝试其他属性
      }

      // 尝试获取 dataList 属性
      try {
        final dataList = dynamicLogic.dataList;
        if (dataList != null && dataList is List) {
          return List<T>.from(dataList);
        }
      } catch (e) {
        // dataList 属性不存在，继续尝试其他属性
      }

      // 尝试获取 data 属性
      try {
        final data = dynamicLogic.data;
        if (data != null && data is List) {
          return List<T>.from(data);
        }
      } catch (e) {
        // data 属性不存在
      }
    } catch (e) {
      // 如果所有尝试都失败，返回空列表
    }

    return [];
  }
}
