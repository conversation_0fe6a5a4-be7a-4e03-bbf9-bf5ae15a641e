import 'package:flutter/material.dart';

/// Base组件的本地化文本
class BaseLocalizations {
  BaseLocalizations(this.locale);
  
  final Locale locale;
  
  static BaseLocalizations of(BuildContext context) {
    return Localizations.of<BaseLocalizations>(context, BaseLocalizations) ??
           BaseLocalizations(const Locale('en'));
  }
  
  static const LocalizationsDelegate<BaseLocalizations> delegate = _BaseLocalizationsDelegate();
  
  // 加载状态文本
  String get loading => _getText('loading');
  String get loadingMore => _getText('loadingMore');
  String get refreshing => _getText('refreshing');
  
  // 错误状态文本
  String get error => _getText('error');
  String get networkError => _getText('networkError');
  String get serverError => _getText('serverError');
  String get unknownError => _getText('unknownError');
  
  // 空状态文本
  String get empty => _getText('empty');
  String get noData => _getText('noData');
  String get noMoreData => _getText('noMoreData');
  
  // 操作按钮文本
  String get retry => _getText('retry');
  String get refresh => _getText('refresh');
  String get loadMore => _getText('loadMore');
  
  // 通用消息
  String get somethingWentWrong => _getText('somethingWentWrong');
  String get noDataAvailable => _getText('noDataAvailable');
  String get networkConnectionFailed => _getText('networkConnectionFailed');
  String get serverErrorOccurred => _getText('serverErrorOccurred');
  
  String _getText(String key) {
    return _localizedValues[locale.languageCode]?[key] ?? 
           _localizedValues['en']![key]!;
  }
  
  static const Map<String, Map<String, String>> _localizedValues = {
    'en': {
      'loading': 'Loading...',
      'loadingMore': 'Loading more...',
      'refreshing': 'Refreshing...',
      'error': 'Error',
      'networkError': 'Network Error',
      'serverError': 'Server Error',
      'unknownError': 'Unknown Error',
      'empty': 'Empty',
      'noData': 'No Data',
      'noMoreData': 'No more data',
      'retry': 'Retry',
      'refresh': 'Refresh',
      'loadMore': 'Load More',
      'somethingWentWrong': 'Something went wrong',
      'noDataAvailable': 'No data available',
      'networkConnectionFailed': 'Network connection failed',
      'serverErrorOccurred': 'Server error occurred',
    },
    'zh': {
      'loading': '加载中...',
      'loadingMore': '加载更多...',
      'refreshing': '刷新中...',
      'error': '错误',
      'networkError': '网络错误',
      'serverError': '服务器错误',
      'unknownError': '未知错误',
      'empty': '空',
      'noData': '暂无数据',
      'noMoreData': '没有更多数据了',
      'retry': '重试',
      'refresh': '刷新',
      'loadMore': '加载更多',
      'somethingWentWrong': '出现了问题',
      'noDataAvailable': '暂无数据，点击刷新',
      'networkConnectionFailed': '网络连接失败',
      'serverErrorOccurred': '服务器错误',
    },
  };
}

class _BaseLocalizationsDelegate extends LocalizationsDelegate<BaseLocalizations> {
  const _BaseLocalizationsDelegate();
  
  @override
  bool isSupported(Locale locale) {
    return BaseLocalizations._localizedValues.containsKey(locale.languageCode);
  }
  
  @override
  Future<BaseLocalizations> load(Locale locale) async {
    return BaseLocalizations(locale);
  }
  
  @override
  bool shouldReload(_BaseLocalizationsDelegate old) => false;
}
