import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/enterprise_detail_entity.dart';

EnterpriseDetailEntity $EnterpriseDetailEntityFromJson(
    Map<String, dynamic> json) {
  final EnterpriseDetailEntity enterpriseDetailEntity = EnterpriseDetailEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    enterpriseDetailEntity.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['UserId']);
  if (userId != null) {
    enterpriseDetailEntity.userId = userId;
  }
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    enterpriseDetailEntity.name = name;
  }
  final String? intro = jsonConvert.convert<String>(json['Intro']);
  if (intro != null) {
    enterpriseDetailEntity.intro = intro;
  }
  final String? industry = jsonConvert.convert<String>(json['Industry']);
  if (industry != null) {
    enterpriseDetailEntity.industry = industry;
  }
  final String? nature = jsonConvert.convert<String>(json['Nature']);
  if (nature != null) {
    enterpriseDetailEntity.nature = nature;
  }
  final String? websiteUrl = jsonConvert.convert<String>(json['WebsiteUrl']);
  if (websiteUrl != null) {
    enterpriseDetailEntity.websiteUrl = websiteUrl;
  }
  final String? busRoutes = jsonConvert.convert<String>(json['BusRoutes']);
  if (busRoutes != null) {
    enterpriseDetailEntity.busRoutes = busRoutes;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    enterpriseDetailEntity.mapLocation = mapLocation;
  }
  final String? logo = jsonConvert.convert<String>(json['Logo']);
  if (logo != null) {
    enterpriseDetailEntity.logo = logo;
  }
  final String? logoLit = jsonConvert.convert<String>(json['LogoLit']);
  if (logoLit != null) {
    enterpriseDetailEntity.logoLit = logoLit;
  }
  final String? address = jsonConvert.convert<String>(json['Address']);
  if (address != null) {
    enterpriseDetailEntity.address = address;
  }
  final String? peopleNum = jsonConvert.convert<String>(json['PeopleNum']);
  if (peopleNum != null) {
    enterpriseDetailEntity.peopleNum = peopleNum;
  }
  final String? registeredCapital = jsonConvert.convert<String>(
      json['RegisteredCapital']);
  if (registeredCapital != null) {
    enterpriseDetailEntity.registeredCapital = registeredCapital;
  }
  final List<dynamic>? images = (json['Images'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (images != null) {
    enterpriseDetailEntity.images = images;
  }
  final List<dynamic>? videos = (json['Videos'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (videos != null) {
    enterpriseDetailEntity.videos = videos;
  }
  final String? contactManName = jsonConvert.convert<String>(
      json['ContactManName']);
  if (contactManName != null) {
    enterpriseDetailEntity.contactManName = contactManName;
  }
  final String? contactManPhone = jsonConvert.convert<String>(
      json['ContactManPhone']);
  if (contactManPhone != null) {
    enterpriseDetailEntity.contactManPhone = contactManPhone;
  }
  final List<EnterpriseDetailJobs>? jobs = (json['Jobs'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<EnterpriseDetailJobs>(e) as EnterpriseDetailJobs)
      .toList();
  if (jobs != null) {
    enterpriseDetailEntity.jobs = jobs;
  }
  final bool? isSelf = jsonConvert.convert<bool>(json['IsSelf']);
  if (isSelf != null) {
    enterpriseDetailEntity.isSelf = isSelf;
  }
  return enterpriseDetailEntity;
}

Map<String, dynamic> $EnterpriseDetailEntityToJson(
    EnterpriseDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['UserId'] = entity.userId;
  data['Name'] = entity.name;
  data['Intro'] = entity.intro;
  data['Industry'] = entity.industry;
  data['Nature'] = entity.nature;
  data['WebsiteUrl'] = entity.websiteUrl;
  data['BusRoutes'] = entity.busRoutes;
  data['MapLocation'] = entity.mapLocation;
  data['Logo'] = entity.logo;
  data['LogoLit'] = entity.logoLit;
  data['Address'] = entity.address;
  data['PeopleNum'] = entity.peopleNum;
  data['RegisteredCapital'] = entity.registeredCapital;
  data['Images'] = entity.images;
  data['Videos'] = entity.videos;
  data['ContactManName'] = entity.contactManName;
  data['ContactManPhone'] = entity.contactManPhone;
  data['Jobs'] = entity.jobs.map((v) => v.toJson()).toList();
  data['IsSelf'] = entity.isSelf;
  return data;
}

extension EnterpriseDetailEntityExtension on EnterpriseDetailEntity {
  EnterpriseDetailEntity copyWith({
    String? id,
    String? userId,
    String? name,
    String? intro,
    String? industry,
    String? nature,
    String? websiteUrl,
    String? busRoutes,
    String? mapLocation,
    String? logo,
    String? logoLit,
    String? address,
    String? peopleNum,
    String? registeredCapital,
    List<dynamic>? images,
    List<dynamic>? videos,
    String? contactManName,
    String? contactManPhone,
    List<EnterpriseDetailJobs>? jobs,
    bool? isSelf,
  }) {
    return EnterpriseDetailEntity()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..name = name ?? this.name
      ..intro = intro ?? this.intro
      ..industry = industry ?? this.industry
      ..nature = nature ?? this.nature
      ..websiteUrl = websiteUrl ?? this.websiteUrl
      ..busRoutes = busRoutes ?? this.busRoutes
      ..mapLocation = mapLocation ?? this.mapLocation
      ..logo = logo ?? this.logo
      ..logoLit = logoLit ?? this.logoLit
      ..address = address ?? this.address
      ..peopleNum = peopleNum ?? this.peopleNum
      ..registeredCapital = registeredCapital ?? this.registeredCapital
      ..images = images ?? this.images
      ..videos = videos ?? this.videos
      ..contactManName = contactManName ?? this.contactManName
      ..contactManPhone = contactManPhone ?? this.contactManPhone
      ..jobs = jobs ?? this.jobs
      ..isSelf = isSelf ?? this.isSelf;
  }
}

EnterpriseDetailJobs $EnterpriseDetailJobsFromJson(Map<String, dynamic> json) {
  final EnterpriseDetailJobs enterpriseDetailJobs = EnterpriseDetailJobs();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    enterpriseDetailJobs.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    enterpriseDetailJobs.name = name;
  }
  final String? pay = jsonConvert.convert<String>(json['Pay']);
  if (pay != null) {
    enterpriseDetailJobs.pay = pay;
  }
  final String? jobPayUnit = jsonConvert.convert<String>(json['JobPayUnit']);
  if (jobPayUnit != null) {
    enterpriseDetailJobs.jobPayUnit = jobPayUnit;
  }
  final String? userId = jsonConvert.convert<String>(json['UserId']);
  if (userId != null) {
    enterpriseDetailJobs.userId = userId;
  }
  final String? enterpriseId = jsonConvert.convert<String>(
      json['EnterpriseId']);
  if (enterpriseId != null) {
    enterpriseDetailJobs.enterpriseId = enterpriseId;
  }
  final String? enterpriseName = jsonConvert.convert<String>(
      json['EnterpriseName']);
  if (enterpriseName != null) {
    enterpriseDetailJobs.enterpriseName = enterpriseName;
  }
  final List<
      EnterpriseDetailJobsUserAuths>? userAuths = (json['UserAuths'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<EnterpriseDetailJobsUserAuths>(
          e) as EnterpriseDetailJobsUserAuths).toList();
  if (userAuths != null) {
    enterpriseDetailJobs.userAuths = userAuths;
  }
  final String? enterpriseNatureCode = jsonConvert.convert<String>(
      json['EnterpriseNatureCode']);
  if (enterpriseNatureCode != null) {
    enterpriseDetailJobs.enterpriseNatureCode = enterpriseNatureCode;
  }
  final String? enterpriseRegisteredCapitalCode = jsonConvert.convert<String>(
      json['EnterpriseRegisteredCapitalCode']);
  if (enterpriseRegisteredCapitalCode != null) {
    enterpriseDetailJobs.enterpriseRegisteredCapitalCode =
        enterpriseRegisteredCapitalCode;
  }
  final String? enterprisePeopleNumCode = jsonConvert.convert<String>(
      json['EnterprisePeopleNumCode']);
  if (enterprisePeopleNumCode != null) {
    enterpriseDetailJobs.enterprisePeopleNumCode = enterprisePeopleNumCode;
  }
  final String? enterpriseLogoSmall = jsonConvert.convert<String>(
      json['EnterpriseLogoSmall']);
  if (enterpriseLogoSmall != null) {
    enterpriseDetailJobs.enterpriseLogoSmall = enterpriseLogoSmall;
  }
  final String? shareLogo = jsonConvert.convert<String>(json['ShareLogo']);
  if (shareLogo != null) {
    enterpriseDetailJobs.shareLogo = shareLogo;
  }
  final String? welfare = jsonConvert.convert<String>(json['Welfare']);
  if (welfare != null) {
    enterpriseDetailJobs.welfare = welfare;
  }
  final String? welfareValue = jsonConvert.convert<String>(
      json['WelfareValue']);
  if (welfareValue != null) {
    enterpriseDetailJobs.welfareValue = welfareValue;
  }
  final String? releaseTime = jsonConvert.convert<String>(json['ReleaseTime']);
  if (releaseTime != null) {
    enterpriseDetailJobs.releaseTime = releaseTime;
  }
  final String? mapLng = jsonConvert.convert<String>(json['MapLng']);
  if (mapLng != null) {
    enterpriseDetailJobs.mapLng = mapLng;
  }
  final String? mapLat = jsonConvert.convert<String>(json['MapLat']);
  if (mapLat != null) {
    enterpriseDetailJobs.mapLat = mapLat;
  }
  final int? distance = jsonConvert.convert<int>(json['Distance']);
  if (distance != null) {
    enterpriseDetailJobs.distance = distance;
  }
  final int? recruitingCount = jsonConvert.convert<int>(
      json['RecruitingCount']);
  if (recruitingCount != null) {
    enterpriseDetailJobs.recruitingCount = recruitingCount;
  }
  final String? jobTypeId = jsonConvert.convert<String>(json['JobTypeId']);
  if (jobTypeId != null) {
    enterpriseDetailJobs.jobTypeId = jobTypeId;
  }
  final String? jobTypeName = jsonConvert.convert<String>(json['JobTypeName']);
  if (jobTypeName != null) {
    enterpriseDetailJobs.jobTypeName = jobTypeName;
  }
  final String? nature = jsonConvert.convert<String>(json['Nature']);
  if (nature != null) {
    enterpriseDetailJobs.nature = nature;
  }
  final String? department = jsonConvert.convert<String>(json['Department']);
  if (department != null) {
    enterpriseDetailJobs.department = department;
  }
  final String? payWay = jsonConvert.convert<String>(json['PayWay']);
  if (payWay != null) {
    enterpriseDetailJobs.payWay = payWay;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    enterpriseDetailJobs.mapLocation = mapLocation;
  }
  final String? demandEducationCode = jsonConvert.convert<String>(
      json['DemandEducationCode']);
  if (demandEducationCode != null) {
    enterpriseDetailJobs.demandEducationCode = demandEducationCode;
  }
  final String? describe = jsonConvert.convert<String>(json['Describe']);
  if (describe != null) {
    enterpriseDetailJobs.describe = describe;
  }
  final int? applyCount = jsonConvert.convert<int>(json['ApplyCount']);
  if (applyCount != null) {
    enterpriseDetailJobs.applyCount = applyCount;
  }
  final String? accommodation = jsonConvert.convert<String>(
      json['Accommodation']);
  if (accommodation != null) {
    enterpriseDetailJobs.accommodation = accommodation;
  }
  final String? workAddress = jsonConvert.convert<String>(json['WorkAddress']);
  if (workAddress != null) {
    enterpriseDetailJobs.workAddress = workAddress;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['IsHot']);
  if (isHot != null) {
    enterpriseDetailJobs.isHot = isHot;
  }
  final bool? isSelf = jsonConvert.convert<bool>(json['IsSelf']);
  if (isSelf != null) {
    enterpriseDetailJobs.isSelf = isSelf;
  }
  final String? contactManName = jsonConvert.convert<String>(
      json['ContactManName']);
  if (contactManName != null) {
    enterpriseDetailJobs.contactManName = contactManName;
  }
  final String? contactManPhone = jsonConvert.convert<String>(
      json['ContactManPhone']);
  if (contactManPhone != null) {
    enterpriseDetailJobs.contactManPhone = contactManPhone;
  }
  final bool? isPutaway = jsonConvert.convert<bool>(json['IsPutaway']);
  if (isPutaway != null) {
    enterpriseDetailJobs.isPutaway = isPutaway;
  }
  final int? resumeCount = jsonConvert.convert<int>(json['ResumeCount']);
  if (resumeCount != null) {
    enterpriseDetailJobs.resumeCount = resumeCount;
  }
  final String? releaseChannel = jsonConvert.convert<String>(
      json['ReleaseChannel']);
  if (releaseChannel != null) {
    enterpriseDetailJobs.releaseChannel = releaseChannel;
  }
  final String? street = jsonConvert.convert<String>(json['Street']);
  if (street != null) {
    enterpriseDetailJobs.street = street;
  }
  return enterpriseDetailJobs;
}

Map<String, dynamic> $EnterpriseDetailJobsToJson(EnterpriseDetailJobs entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['Name'] = entity.name;
  data['Pay'] = entity.pay;
  data['JobPayUnit'] = entity.jobPayUnit;
  data['UserId'] = entity.userId;
  data['EnterpriseId'] = entity.enterpriseId;
  data['EnterpriseName'] = entity.enterpriseName;
  data['UserAuths'] = entity.userAuths.map((v) => v.toJson()).toList();
  data['EnterpriseNatureCode'] = entity.enterpriseNatureCode;
  data['EnterpriseRegisteredCapitalCode'] =
      entity.enterpriseRegisteredCapitalCode;
  data['EnterprisePeopleNumCode'] = entity.enterprisePeopleNumCode;
  data['EnterpriseLogoSmall'] = entity.enterpriseLogoSmall;
  data['ShareLogo'] = entity.shareLogo;
  data['Welfare'] = entity.welfare;
  data['WelfareValue'] = entity.welfareValue;
  data['ReleaseTime'] = entity.releaseTime;
  data['MapLng'] = entity.mapLng;
  data['MapLat'] = entity.mapLat;
  data['Distance'] = entity.distance;
  data['RecruitingCount'] = entity.recruitingCount;
  data['JobTypeId'] = entity.jobTypeId;
  data['JobTypeName'] = entity.jobTypeName;
  data['Nature'] = entity.nature;
  data['Department'] = entity.department;
  data['PayWay'] = entity.payWay;
  data['MapLocation'] = entity.mapLocation;
  data['DemandEducationCode'] = entity.demandEducationCode;
  data['Describe'] = entity.describe;
  data['ApplyCount'] = entity.applyCount;
  data['Accommodation'] = entity.accommodation;
  data['WorkAddress'] = entity.workAddress;
  data['IsHot'] = entity.isHot;
  data['IsSelf'] = entity.isSelf;
  data['ContactManName'] = entity.contactManName;
  data['ContactManPhone'] = entity.contactManPhone;
  data['IsPutaway'] = entity.isPutaway;
  data['ResumeCount'] = entity.resumeCount;
  data['ReleaseChannel'] = entity.releaseChannel;
  data['Street'] = entity.street;
  return data;
}

extension EnterpriseDetailJobsExtension on EnterpriseDetailJobs {
  EnterpriseDetailJobs copyWith({
    String? id,
    String? name,
    String? pay,
    String? jobPayUnit,
    String? userId,
    String? enterpriseId,
    String? enterpriseName,
    List<EnterpriseDetailJobsUserAuths>? userAuths,
    String? enterpriseNatureCode,
    String? enterpriseRegisteredCapitalCode,
    String? enterprisePeopleNumCode,
    String? enterpriseLogoSmall,
    String? shareLogo,
    String? welfare,
    String? welfareValue,
    String? releaseTime,
    String? mapLng,
    String? mapLat,
    int? distance,
    int? recruitingCount,
    String? jobTypeId,
    String? jobTypeName,
    String? nature,
    String? department,
    String? payWay,
    String? mapLocation,
    String? demandEducationCode,
    String? describe,
    int? applyCount,
    String? accommodation,
    String? workAddress,
    bool? isHot,
    bool? isSelf,
    String? contactManName,
    String? contactManPhone,
    bool? isPutaway,
    int? resumeCount,
    String? releaseChannel,
    String? street,
  }) {
    return EnterpriseDetailJobs()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..pay = pay ?? this.pay
      ..jobPayUnit = jobPayUnit ?? this.jobPayUnit
      ..userId = userId ?? this.userId
      ..enterpriseId = enterpriseId ?? this.enterpriseId
      ..enterpriseName = enterpriseName ?? this.enterpriseName
      ..userAuths = userAuths ?? this.userAuths
      ..enterpriseNatureCode = enterpriseNatureCode ?? this.enterpriseNatureCode
      ..enterpriseRegisteredCapitalCode = enterpriseRegisteredCapitalCode ??
          this.enterpriseRegisteredCapitalCode
      ..enterprisePeopleNumCode = enterprisePeopleNumCode ??
          this.enterprisePeopleNumCode
      ..enterpriseLogoSmall = enterpriseLogoSmall ?? this.enterpriseLogoSmall
      ..shareLogo = shareLogo ?? this.shareLogo
      ..welfare = welfare ?? this.welfare
      ..welfareValue = welfareValue ?? this.welfareValue
      ..releaseTime = releaseTime ?? this.releaseTime
      ..mapLng = mapLng ?? this.mapLng
      ..mapLat = mapLat ?? this.mapLat
      ..distance = distance ?? this.distance
      ..recruitingCount = recruitingCount ?? this.recruitingCount
      ..jobTypeId = jobTypeId ?? this.jobTypeId
      ..jobTypeName = jobTypeName ?? this.jobTypeName
      ..nature = nature ?? this.nature
      ..department = department ?? this.department
      ..payWay = payWay ?? this.payWay
      ..mapLocation = mapLocation ?? this.mapLocation
      ..demandEducationCode = demandEducationCode ?? this.demandEducationCode
      ..describe = describe ?? this.describe
      ..applyCount = applyCount ?? this.applyCount
      ..accommodation = accommodation ?? this.accommodation
      ..workAddress = workAddress ?? this.workAddress
      ..isHot = isHot ?? this.isHot
      ..isSelf = isSelf ?? this.isSelf
      ..contactManName = contactManName ?? this.contactManName
      ..contactManPhone = contactManPhone ?? this.contactManPhone
      ..isPutaway = isPutaway ?? this.isPutaway
      ..resumeCount = resumeCount ?? this.resumeCount
      ..releaseChannel = releaseChannel ?? this.releaseChannel
      ..street = street ?? this.street;
  }
}

EnterpriseDetailJobsUserAuths $EnterpriseDetailJobsUserAuthsFromJson(
    Map<String, dynamic> json) {
  final EnterpriseDetailJobsUserAuths enterpriseDetailJobsUserAuths = EnterpriseDetailJobsUserAuths();
  final String? type = jsonConvert.convert<String>(json['Type']);
  if (type != null) {
    enterpriseDetailJobsUserAuths.type = type;
  }
  final String? addTime = jsonConvert.convert<String>(json['AddTime']);
  if (addTime != null) {
    enterpriseDetailJobsUserAuths.addTime = addTime;
  }
  return enterpriseDetailJobsUserAuths;
}

Map<String, dynamic> $EnterpriseDetailJobsUserAuthsToJson(
    EnterpriseDetailJobsUserAuths entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Type'] = entity.type;
  data['AddTime'] = entity.addTime;
  return data;
}

extension EnterpriseDetailJobsUserAuthsExtension on EnterpriseDetailJobsUserAuths {
  EnterpriseDetailJobsUserAuths copyWith({
    String? type,
    String? addTime,
  }) {
    return EnterpriseDetailJobsUserAuths()
      ..type = type ?? this.type
      ..addTime = addTime ?? this.addTime;
  }
}