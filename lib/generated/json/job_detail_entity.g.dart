import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/job_detail_entity.dart';

JobDetailEntity $JobDetailEntityFromJson(Map<String, dynamic> json) {
  final JobDetailEntity jobDetailEntity = JobDetailEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    jobDetailEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    jobDetailEntity.name = name;
  }
  final String? pay = jsonConvert.convert<String>(json['Pay']);
  if (pay != null) {
    jobDetailEntity.pay = pay;
  }
  final String? jobPayUnit = jsonConvert.convert<String>(json['JobPayUnit']);
  if (jobPayUnit != null) {
    jobDetailEntity.jobPayUnit = jobPayUnit;
  }
  final String? userId = jsonConvert.convert<String>(json['UserId']);
  if (userId != null) {
    jobDetailEntity.userId = userId;
  }
  final String? enterpriseId = jsonConvert.convert<String>(
      json['EnterpriseId']);
  if (enterpriseId != null) {
    jobDetailEntity.enterpriseId = enterpriseId;
  }
  final String? enterpriseName = jsonConvert.convert<String>(
      json['EnterpriseName']);
  if (enterpriseName != null) {
    jobDetailEntity.enterpriseName = enterpriseName;
  }
  final List<JobDetailUserAuths>? userAuths = (json['UserAuths'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<JobDetailUserAuths>(e) as JobDetailUserAuths)
      .toList();
  if (userAuths != null) {
    jobDetailEntity.userAuths = userAuths;
  }
  final String? enterpriseNatureCode = jsonConvert.convert<String>(
      json['EnterpriseNatureCode']);
  if (enterpriseNatureCode != null) {
    jobDetailEntity.enterpriseNatureCode = enterpriseNatureCode;
  }
  final String? enterpriseRegisteredCapitalCode = jsonConvert.convert<String>(
      json['EnterpriseRegisteredCapitalCode']);
  if (enterpriseRegisteredCapitalCode != null) {
    jobDetailEntity.enterpriseRegisteredCapitalCode =
        enterpriseRegisteredCapitalCode;
  }
  final String? enterprisePeopleNumCode = jsonConvert.convert<String>(
      json['EnterprisePeopleNumCode']);
  if (enterprisePeopleNumCode != null) {
    jobDetailEntity.enterprisePeopleNumCode = enterprisePeopleNumCode;
  }
  final String? enterpriseLogoSmall = jsonConvert.convert<String>(
      json['EnterpriseLogoSmall']);
  if (enterpriseLogoSmall != null) {
    jobDetailEntity.enterpriseLogoSmall = enterpriseLogoSmall;
  }
  final String? shareLogo = jsonConvert.convert<String>(json['ShareLogo']);
  if (shareLogo != null) {
    jobDetailEntity.shareLogo = shareLogo;
  }
  final String? welfare = jsonConvert.convert<String>(json['Welfare']);
  if (welfare != null) {
    jobDetailEntity.welfare = welfare;
  }
  final String? welfareValue = jsonConvert.convert<String>(
      json['WelfareValue']);
  if (welfareValue != null) {
    jobDetailEntity.welfareValue = welfareValue;
  }
  final String? releaseTime = jsonConvert.convert<String>(json['ReleaseTime']);
  if (releaseTime != null) {
    jobDetailEntity.releaseTime = releaseTime;
  }
  final String? mapLng = jsonConvert.convert<String>(json['MapLng']);
  if (mapLng != null) {
    jobDetailEntity.mapLng = mapLng;
  }
  final String? mapLat = jsonConvert.convert<String>(json['MapLat']);
  if (mapLat != null) {
    jobDetailEntity.mapLat = mapLat;
  }
  final int? distance = jsonConvert.convert<int>(json['Distance']);
  if (distance != null) {
    jobDetailEntity.distance = distance;
  }
  final int? recruitingCount = jsonConvert.convert<int>(
      json['RecruitingCount']);
  if (recruitingCount != null) {
    jobDetailEntity.recruitingCount = recruitingCount;
  }
  final String? jobTypeId = jsonConvert.convert<String>(json['JobTypeId']);
  if (jobTypeId != null) {
    jobDetailEntity.jobTypeId = jobTypeId;
  }
  final String? jobTypeName = jsonConvert.convert<String>(json['JobTypeName']);
  if (jobTypeName != null) {
    jobDetailEntity.jobTypeName = jobTypeName;
  }
  final String? nature = jsonConvert.convert<String>(json['Nature']);
  if (nature != null) {
    jobDetailEntity.nature = nature;
  }
  final String? department = jsonConvert.convert<String>(json['Department']);
  if (department != null) {
    jobDetailEntity.department = department;
  }
  final String? payWay = jsonConvert.convert<String>(json['PayWay']);
  if (payWay != null) {
    jobDetailEntity.payWay = payWay;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    jobDetailEntity.mapLocation = mapLocation;
  }
  final String? demandEducationCode = jsonConvert.convert<String>(
      json['DemandEducationCode']);
  if (demandEducationCode != null) {
    jobDetailEntity.demandEducationCode = demandEducationCode;
  }
  final String? street = jsonConvert.convert<String>(json['Street']);
  if (street != null) {
    jobDetailEntity.street = street;
  }
  final String? describe = jsonConvert.convert<String>(json['Describe']);
  if (describe != null) {
    jobDetailEntity.describe = describe;
  }
  final int? applyCount = jsonConvert.convert<int>(json['ApplyCount']);
  if (applyCount != null) {
    jobDetailEntity.applyCount = applyCount;
  }
  final String? accommodation = jsonConvert.convert<String>(
      json['Accommodation']);
  if (accommodation != null) {
    jobDetailEntity.accommodation = accommodation;
  }
  final String? workAddress = jsonConvert.convert<String>(json['WorkAddress']);
  if (workAddress != null) {
    jobDetailEntity.workAddress = workAddress;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['IsHot']);
  if (isHot != null) {
    jobDetailEntity.isHot = isHot;
  }
  final bool? isSelf = jsonConvert.convert<bool>(json['IsSelf']);
  if (isSelf != null) {
    jobDetailEntity.isSelf = isSelf;
  }
  final String? contactManName = jsonConvert.convert<String>(
      json['ContactManName']);
  if (contactManName != null) {
    jobDetailEntity.contactManName = contactManName;
  }
  final String? contactManPhone = jsonConvert.convert<String>(
      json['ContactManPhone']);
  if (contactManPhone != null) {
    jobDetailEntity.contactManPhone = contactManPhone;
  }
  final bool? isPutaway = jsonConvert.convert<bool>(json['IsPutaway']);
  if (isPutaway != null) {
    jobDetailEntity.isPutaway = isPutaway;
  }
  final int? resumeCount = jsonConvert.convert<int>(json['ResumeCount']);
  if (resumeCount != null) {
    jobDetailEntity.resumeCount = resumeCount;
  }
  final String? releaseChannel = jsonConvert.convert<String>(
      json['ReleaseChannel']);
  if (releaseChannel != null) {
    jobDetailEntity.releaseChannel = releaseChannel;
  }
  return jobDetailEntity;
}

Map<String, dynamic> $JobDetailEntityToJson(JobDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['Name'] = entity.name;
  data['Pay'] = entity.pay;
  data['JobPayUnit'] = entity.jobPayUnit;
  data['UserId'] = entity.userId;
  data['EnterpriseId'] = entity.enterpriseId;
  data['EnterpriseName'] = entity.enterpriseName;
  data['UserAuths'] = entity.userAuths.map((v) => v.toJson()).toList();
  data['EnterpriseNatureCode'] = entity.enterpriseNatureCode;
  data['EnterpriseRegisteredCapitalCode'] =
      entity.enterpriseRegisteredCapitalCode;
  data['EnterprisePeopleNumCode'] = entity.enterprisePeopleNumCode;
  data['EnterpriseLogoSmall'] = entity.enterpriseLogoSmall;
  data['ShareLogo'] = entity.shareLogo;
  data['Welfare'] = entity.welfare;
  data['WelfareValue'] = entity.welfareValue;
  data['ReleaseTime'] = entity.releaseTime;
  data['MapLng'] = entity.mapLng;
  data['MapLat'] = entity.mapLat;
  data['Distance'] = entity.distance;
  data['RecruitingCount'] = entity.recruitingCount;
  data['JobTypeId'] = entity.jobTypeId;
  data['JobTypeName'] = entity.jobTypeName;
  data['Nature'] = entity.nature;
  data['Department'] = entity.department;
  data['PayWay'] = entity.payWay;
  data['MapLocation'] = entity.mapLocation;
  data['DemandEducationCode'] = entity.demandEducationCode;
  data['Street'] = entity.street;
  data['Describe'] = entity.describe;
  data['ApplyCount'] = entity.applyCount;
  data['Accommodation'] = entity.accommodation;
  data['WorkAddress'] = entity.workAddress;
  data['IsHot'] = entity.isHot;
  data['IsSelf'] = entity.isSelf;
  data['ContactManName'] = entity.contactManName;
  data['ContactManPhone'] = entity.contactManPhone;
  data['IsPutaway'] = entity.isPutaway;
  data['ResumeCount'] = entity.resumeCount;
  data['ReleaseChannel'] = entity.releaseChannel;
  return data;
}

extension JobDetailEntityExtension on JobDetailEntity {
  JobDetailEntity copyWith({
    String? id,
    String? name,
    String? pay,
    String? jobPayUnit,
    String? userId,
    String? enterpriseId,
    String? enterpriseName,
    List<JobDetailUserAuths>? userAuths,
    String? enterpriseNatureCode,
    String? enterpriseRegisteredCapitalCode,
    String? enterprisePeopleNumCode,
    String? enterpriseLogoSmall,
    String? shareLogo,
    String? welfare,
    String? welfareValue,
    String? releaseTime,
    String? mapLng,
    String? mapLat,
    int? distance,
    int? recruitingCount,
    String? jobTypeId,
    String? jobTypeName,
    String? nature,
    String? department,
    String? payWay,
    String? mapLocation,
    String? demandEducationCode,
    String? street,
    String? describe,
    int? applyCount,
    String? accommodation,
    String? workAddress,
    bool? isHot,
    bool? isSelf,
    String? contactManName,
    String? contactManPhone,
    bool? isPutaway,
    int? resumeCount,
    String? releaseChannel,
  }) {
    return JobDetailEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..pay = pay ?? this.pay
      ..jobPayUnit = jobPayUnit ?? this.jobPayUnit
      ..userId = userId ?? this.userId
      ..enterpriseId = enterpriseId ?? this.enterpriseId
      ..enterpriseName = enterpriseName ?? this.enterpriseName
      ..userAuths = userAuths ?? this.userAuths
      ..enterpriseNatureCode = enterpriseNatureCode ?? this.enterpriseNatureCode
      ..enterpriseRegisteredCapitalCode = enterpriseRegisteredCapitalCode ??
          this.enterpriseRegisteredCapitalCode
      ..enterprisePeopleNumCode = enterprisePeopleNumCode ??
          this.enterprisePeopleNumCode
      ..enterpriseLogoSmall = enterpriseLogoSmall ?? this.enterpriseLogoSmall
      ..shareLogo = shareLogo ?? this.shareLogo
      ..welfare = welfare ?? this.welfare
      ..welfareValue = welfareValue ?? this.welfareValue
      ..releaseTime = releaseTime ?? this.releaseTime
      ..mapLng = mapLng ?? this.mapLng
      ..mapLat = mapLat ?? this.mapLat
      ..distance = distance ?? this.distance
      ..recruitingCount = recruitingCount ?? this.recruitingCount
      ..jobTypeId = jobTypeId ?? this.jobTypeId
      ..jobTypeName = jobTypeName ?? this.jobTypeName
      ..nature = nature ?? this.nature
      ..department = department ?? this.department
      ..payWay = payWay ?? this.payWay
      ..mapLocation = mapLocation ?? this.mapLocation
      ..demandEducationCode = demandEducationCode ?? this.demandEducationCode
      ..street = street ?? this.street
      ..describe = describe ?? this.describe
      ..applyCount = applyCount ?? this.applyCount
      ..accommodation = accommodation ?? this.accommodation
      ..workAddress = workAddress ?? this.workAddress
      ..isHot = isHot ?? this.isHot
      ..isSelf = isSelf ?? this.isSelf
      ..contactManName = contactManName ?? this.contactManName
      ..contactManPhone = contactManPhone ?? this.contactManPhone
      ..isPutaway = isPutaway ?? this.isPutaway
      ..resumeCount = resumeCount ?? this.resumeCount
      ..releaseChannel = releaseChannel ?? this.releaseChannel;
  }
}

JobDetailUserAuths $JobDetailUserAuthsFromJson(Map<String, dynamic> json) {
  final JobDetailUserAuths jobDetailUserAuths = JobDetailUserAuths();
  final String? type = jsonConvert.convert<String>(json['Type']);
  if (type != null) {
    jobDetailUserAuths.type = type;
  }
  final String? addTime = jsonConvert.convert<String>(json['AddTime']);
  if (addTime != null) {
    jobDetailUserAuths.addTime = addTime;
  }
  return jobDetailUserAuths;
}

Map<String, dynamic> $JobDetailUserAuthsToJson(JobDetailUserAuths entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Type'] = entity.type;
  data['AddTime'] = entity.addTime;
  return data;
}

extension JobDetailUserAuthsExtension on JobDetailUserAuths {
  JobDetailUserAuths copyWith({
    String? type,
    String? addTime,
  }) {
    return JobDetailUserAuths()
      ..type = type ?? this.type
      ..addTime = addTime ?? this.addTime;
  }
}