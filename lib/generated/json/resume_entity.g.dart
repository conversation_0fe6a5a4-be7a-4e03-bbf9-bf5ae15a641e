import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/resume_entity.dart';

ResumeEntity $ResumeEntityFromJson(Map<String, dynamic> json) {
  final ResumeEntity resumeEntity = ResumeEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    resumeEntity.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['Title']);
  if (title != null) {
    resumeEntity.title = title;
  }
  final String? realname = jsonConvert.convert<String>(json['Realname']);
  if (realname != null) {
    resumeEntity.realname = realname;
  }
  final List<dynamic>? userAuths = (json['UserAuths'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (userAuths != null) {
    resumeEntity.userAuths = userAuths;
  }
  final String? genderCode = jsonConvert.convert<String>(json['GenderCode']);
  if (genderCode != null) {
    resumeEntity.genderCode = genderCode;
  }
  final String? nationCode = jsonConvert.convert<String>(json['NationCode']);
  if (nationCode != null) {
    resumeEntity.nationCode = nationCode;
  }
  final String? maritalStatusCode = jsonConvert.convert<String>(
      json['MaritalStatusCode']);
  if (maritalStatusCode != null) {
    resumeEntity.maritalStatusCode = maritalStatusCode;
  }
  final int? stature = jsonConvert.convert<int>(json['Stature']);
  if (stature != null) {
    resumeEntity.stature = stature;
  }
  final String? address = jsonConvert.convert<String>(json['Address']);
  if (address != null) {
    resumeEntity.address = address;
  }
  final String? computerLevelCode = jsonConvert.convert<String>(
      json['ComputerLevelCode']);
  if (computerLevelCode != null) {
    resumeEntity.computerLevelCode = computerLevelCode;
  }
  final String? englishLevelCode = jsonConvert.convert<String>(
      json['EnglishLevelCode']);
  if (englishLevelCode != null) {
    resumeEntity.englishLevelCode = englishLevelCode;
  }
  final String? nativePlaceAreaId = jsonConvert.convert<String>(
      json['NativePlaceAreaId']);
  if (nativePlaceAreaId != null) {
    resumeEntity.nativePlaceAreaId = nativePlaceAreaId;
  }
  final String? nativePlaceAreaName = jsonConvert.convert<String>(
      json['NativePlaceAreaName']);
  if (nativePlaceAreaName != null) {
    resumeEntity.nativePlaceAreaName = nativePlaceAreaName;
  }
  final String? nativePlaceAreaCascadeName = jsonConvert.convert<String>(
      json['NativePlaceAreaCascadeName']);
  if (nativePlaceAreaCascadeName != null) {
    resumeEntity.nativePlaceAreaCascadeName = nativePlaceAreaCascadeName;
  }
  final String? graduateSchool = jsonConvert.convert<String>(
      json['GraduateSchool']);
  if (graduateSchool != null) {
    resumeEntity.graduateSchool = graduateSchool;
  }
  final String? majorIn = jsonConvert.convert<String>(json['MajorIn']);
  if (majorIn != null) {
    resumeEntity.majorIn = majorIn;
  }
  final String? headImage = jsonConvert.convert<String>(json['HeadImage']);
  if (headImage != null) {
    resumeEntity.headImage = headImage;
  }
  final String? idcard = jsonConvert.convert<String>(json['Idcard']);
  if (idcard != null) {
    resumeEntity.idcard = idcard;
  }
  final String? gender = jsonConvert.convert<String>(json['Gender']);
  if (gender != null) {
    resumeEntity.gender = gender;
  }
  final String? nation = jsonConvert.convert<String>(json['Nation']);
  if (nation != null) {
    resumeEntity.nation = nation;
  }
  final String? qQ = jsonConvert.convert<String>(json['QQ']);
  if (qQ != null) {
    resumeEntity.qQ = qQ;
  }
  final String? birthday = jsonConvert.convert<String>(json['Birthday']);
  if (birthday != null) {
    resumeEntity.birthday = birthday;
  }
  final String? liveAreaId = jsonConvert.convert<String>(json['LiveAreaId']);
  if (liveAreaId != null) {
    resumeEntity.liveAreaId = liveAreaId;
  }
  final String? liveAreaName = jsonConvert.convert<String>(
      json['LiveAreaName']);
  if (liveAreaName != null) {
    resumeEntity.liveAreaName = liveAreaName;
  }
  final String? liveAreaCascadeName = jsonConvert.convert<String>(
      json['LiveAreaCascadeName']);
  if (liveAreaCascadeName != null) {
    resumeEntity.liveAreaCascadeName = liveAreaCascadeName;
  }
  final String? intentionAreaIds = jsonConvert.convert<String>(
      json['IntentionAreaIds']);
  if (intentionAreaIds != null) {
    resumeEntity.intentionAreaIds = intentionAreaIds;
  }
  final String? intentionAreaNames = jsonConvert.convert<String>(
      json['IntentionAreaNames']);
  if (intentionAreaNames != null) {
    resumeEntity.intentionAreaNames = intentionAreaNames;
  }
  final String? intentionJobTypeId = jsonConvert.convert<String>(
      json['IntentionJobTypeId']);
  if (intentionJobTypeId != null) {
    resumeEntity.intentionJobTypeId = intentionJobTypeId;
  }
  final String? intentionJobType = jsonConvert.convert<String>(
      json['IntentionJobType']);
  if (intentionJobType != null) {
    resumeEntity.intentionJobType = intentionJobType;
  }
  final String? intentionJobTypeIds = jsonConvert.convert<String>(
      json['IntentionJobTypeIds']);
  if (intentionJobTypeIds != null) {
    resumeEntity.intentionJobTypeIds = intentionJobTypeIds;
  }
  final String? intentionJobTypeNames = jsonConvert.convert<String>(
      json['IntentionJobTypeNames']);
  if (intentionJobTypeNames != null) {
    resumeEntity.intentionJobTypeNames = intentionJobTypeNames;
  }
  final String? workStatusCode = jsonConvert.convert<String>(
      json['WorkStatusCode']);
  if (workStatusCode != null) {
    resumeEntity.workStatusCode = workStatusCode;
  }
  final String? workingAgeCode = jsonConvert.convert<String>(
      json['WorkingAgeCode']);
  if (workingAgeCode != null) {
    resumeEntity.workingAgeCode = workingAgeCode;
  }
  final String? intentionPayCode = jsonConvert.convert<String>(
      json['IntentionPayCode']);
  if (intentionPayCode != null) {
    resumeEntity.intentionPayCode = intentionPayCode;
  }
  final String? intentionPayValue = jsonConvert.convert<String>(
      json['IntentionPayValue']);
  if (intentionPayValue != null) {
    resumeEntity.intentionPayValue = intentionPayValue;
  }
  final String? intentionPayWayCode = jsonConvert.convert<String>(
      json['IntentionPayWayCode']);
  if (intentionPayWayCode != null) {
    resumeEntity.intentionPayWayCode = intentionPayWayCode;
  }
  final String? educationCode = jsonConvert.convert<String>(
      json['EducationCode']);
  if (educationCode != null) {
    resumeEntity.educationCode = educationCode;
  }
  final List<
      ResumeWorkExperience>? workExperience = (json['WorkExperience'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ResumeWorkExperience>(e) as ResumeWorkExperience)
      .toList();
  if (workExperience != null) {
    resumeEntity.workExperience = workExperience;
  }
  final List<
      ResumeEduExperience>? eduExperience = (json['EduExperience'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ResumeEduExperience>(e) as ResumeEduExperience)
      .toList();
  if (eduExperience != null) {
    resumeEntity.eduExperience = eduExperience;
  }
  final String? personalProfile = jsonConvert.convert<String>(
      json['PersonalProfile']);
  if (personalProfile != null) {
    resumeEntity.personalProfile = personalProfile;
  }
  final double? proportion = jsonConvert.convert<double>(json['Proportion']);
  if (proportion != null) {
    resumeEntity.proportion = proportion;
  }
  final String? certificate = jsonConvert.convert<String>(json['Certificate']);
  if (certificate != null) {
    resumeEntity.certificate = certificate;
  }
  final String? skill = jsonConvert.convert<String>(json['Skill']);
  if (skill != null) {
    resumeEntity.skill = skill;
  }
  final bool? isDefault = jsonConvert.convert<bool>(json['IsDefault']);
  if (isDefault != null) {
    resumeEntity.isDefault = isDefault;
  }
  final int? integral = jsonConvert.convert<int>(json['Integral']);
  if (integral != null) {
    resumeEntity.integral = integral;
  }
  final String? phone = jsonConvert.convert<String>(json['Phone']);
  if (phone != null) {
    resumeEntity.phone = phone;
  }
  final String? email = jsonConvert.convert<String>(json['Email']);
  if (email != null) {
    resumeEntity.email = email;
  }
  final String? userId = jsonConvert.convert<String>(json['UserId']);
  if (userId != null) {
    resumeEntity.userId = userId;
  }
  final String? releaseTime = jsonConvert.convert<String>(json['ReleaseTime']);
  if (releaseTime != null) {
    resumeEntity.releaseTime = releaseTime;
  }
  final String? labels = jsonConvert.convert<String>(json['Labels']);
  if (labels != null) {
    resumeEntity.labels = labels;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    resumeEntity.mapLocation = mapLocation;
  }
  final String? street = jsonConvert.convert<String>(json['Street']);
  if (street != null) {
    resumeEntity.street = street;
  }
  final String? jobSeekerGroupCode = jsonConvert.convert<String>(
      json['JobSeekerGroupCode']);
  if (jobSeekerGroupCode != null) {
    resumeEntity.jobSeekerGroupCode = jobSeekerGroupCode;
  }
  final int? selectCount = jsonConvert.convert<int>(json['SelectCount']);
  if (selectCount != null) {
    resumeEntity.selectCount = selectCount;
  }
  return resumeEntity;
}

Map<String, dynamic> $ResumeEntityToJson(ResumeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['Title'] = entity.title;
  data['Realname'] = entity.realname;
  data['UserAuths'] = entity.userAuths;
  data['GenderCode'] = entity.genderCode;
  data['NationCode'] = entity.nationCode;
  data['MaritalStatusCode'] = entity.maritalStatusCode;
  data['Stature'] = entity.stature;
  data['Address'] = entity.address;
  data['ComputerLevelCode'] = entity.computerLevelCode;
  data['EnglishLevelCode'] = entity.englishLevelCode;
  data['NativePlaceAreaId'] = entity.nativePlaceAreaId;
  data['NativePlaceAreaName'] = entity.nativePlaceAreaName;
  data['NativePlaceAreaCascadeName'] = entity.nativePlaceAreaCascadeName;
  data['GraduateSchool'] = entity.graduateSchool;
  data['MajorIn'] = entity.majorIn;
  data['HeadImage'] = entity.headImage;
  data['Idcard'] = entity.idcard;
  data['Gender'] = entity.gender;
  data['Nation'] = entity.nation;
  data['QQ'] = entity.qQ;
  data['Birthday'] = entity.birthday;
  data['LiveAreaId'] = entity.liveAreaId;
  data['LiveAreaName'] = entity.liveAreaName;
  data['LiveAreaCascadeName'] = entity.liveAreaCascadeName;
  data['IntentionAreaIds'] = entity.intentionAreaIds;
  data['IntentionAreaNames'] = entity.intentionAreaNames;
  data['IntentionJobTypeId'] = entity.intentionJobTypeId;
  data['IntentionJobType'] = entity.intentionJobType;
  data['IntentionJobTypeIds'] = entity.intentionJobTypeIds;
  data['IntentionJobTypeNames'] = entity.intentionJobTypeNames;
  data['WorkStatusCode'] = entity.workStatusCode;
  data['WorkingAgeCode'] = entity.workingAgeCode;
  data['IntentionPayCode'] = entity.intentionPayCode;
  data['IntentionPayValue'] = entity.intentionPayValue;
  data['IntentionPayWayCode'] = entity.intentionPayWayCode;
  data['EducationCode'] = entity.educationCode;
  data['WorkExperience'] =
      entity.workExperience.map((v) => v.toJson()).toList();
  data['EduExperience'] = entity.eduExperience.map((v) => v.toJson()).toList();
  data['PersonalProfile'] = entity.personalProfile;
  data['Proportion'] = entity.proportion;
  data['Certificate'] = entity.certificate;
  data['Skill'] = entity.skill;
  data['IsDefault'] = entity.isDefault;
  data['Integral'] = entity.integral;
  data['Phone'] = entity.phone;
  data['Email'] = entity.email;
  data['UserId'] = entity.userId;
  data['ReleaseTime'] = entity.releaseTime;
  data['Labels'] = entity.labels;
  data['MapLocation'] = entity.mapLocation;
  data['Street'] = entity.street;
  data['JobSeekerGroupCode'] = entity.jobSeekerGroupCode;
  data['SelectCount'] = entity.selectCount;
  return data;
}

extension ResumeEntityExtension on ResumeEntity {
  ResumeEntity copyWith({
    String? id,
    String? title,
    String? realname,
    List<dynamic>? userAuths,
    String? genderCode,
    String? nationCode,
    String? maritalStatusCode,
    int? stature,
    String? address,
    String? computerLevelCode,
    String? englishLevelCode,
    String? nativePlaceAreaId,
    String? nativePlaceAreaName,
    String? nativePlaceAreaCascadeName,
    String? graduateSchool,
    String? majorIn,
    String? headImage,
    String? idcard,
    String? gender,
    String? nation,
    String? qQ,
    String? birthday,
    String? liveAreaId,
    String? liveAreaName,
    String? liveAreaCascadeName,
    String? intentionAreaIds,
    String? intentionAreaNames,
    String? intentionJobTypeId,
    String? intentionJobType,
    String? intentionJobTypeIds,
    String? intentionJobTypeNames,
    String? workStatusCode,
    String? workingAgeCode,
    String? intentionPayCode,
    String? intentionPayValue,
    String? intentionPayWayCode,
    String? educationCode,
    List<ResumeWorkExperience>? workExperience,
    List<ResumeEduExperience>? eduExperience,
    String? personalProfile,
    double? proportion,
    String? certificate,
    String? skill,
    bool? isDefault,
    int? integral,
    String? phone,
    String? email,
    String? userId,
    String? releaseTime,
    String? labels,
    String? mapLocation,
    String? street,
    String? jobSeekerGroupCode,
    int? selectCount,
  }) {
    return ResumeEntity()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..realname = realname ?? this.realname
      ..userAuths = userAuths ?? this.userAuths
      ..genderCode = genderCode ?? this.genderCode
      ..nationCode = nationCode ?? this.nationCode
      ..maritalStatusCode = maritalStatusCode ?? this.maritalStatusCode
      ..stature = stature ?? this.stature
      ..address = address ?? this.address
      ..computerLevelCode = computerLevelCode ?? this.computerLevelCode
      ..englishLevelCode = englishLevelCode ?? this.englishLevelCode
      ..nativePlaceAreaId = nativePlaceAreaId ?? this.nativePlaceAreaId
      ..nativePlaceAreaName = nativePlaceAreaName ?? this.nativePlaceAreaName
      ..nativePlaceAreaCascadeName = nativePlaceAreaCascadeName ??
          this.nativePlaceAreaCascadeName
      ..graduateSchool = graduateSchool ?? this.graduateSchool
      ..majorIn = majorIn ?? this.majorIn
      ..headImage = headImage ?? this.headImage
      ..idcard = idcard ?? this.idcard
      ..gender = gender ?? this.gender
      ..nation = nation ?? this.nation
      ..qQ = qQ ?? this.qQ
      ..birthday = birthday ?? this.birthday
      ..liveAreaId = liveAreaId ?? this.liveAreaId
      ..liveAreaName = liveAreaName ?? this.liveAreaName
      ..liveAreaCascadeName = liveAreaCascadeName ?? this.liveAreaCascadeName
      ..intentionAreaIds = intentionAreaIds ?? this.intentionAreaIds
      ..intentionAreaNames = intentionAreaNames ?? this.intentionAreaNames
      ..intentionJobTypeId = intentionJobTypeId ?? this.intentionJobTypeId
      ..intentionJobType = intentionJobType ?? this.intentionJobType
      ..intentionJobTypeIds = intentionJobTypeIds ?? this.intentionJobTypeIds
      ..intentionJobTypeNames = intentionJobTypeNames ??
          this.intentionJobTypeNames
      ..workStatusCode = workStatusCode ?? this.workStatusCode
      ..workingAgeCode = workingAgeCode ?? this.workingAgeCode
      ..intentionPayCode = intentionPayCode ?? this.intentionPayCode
      ..intentionPayValue = intentionPayValue ?? this.intentionPayValue
      ..intentionPayWayCode = intentionPayWayCode ?? this.intentionPayWayCode
      ..educationCode = educationCode ?? this.educationCode
      ..workExperience = workExperience ?? this.workExperience
      ..eduExperience = eduExperience ?? this.eduExperience
      ..personalProfile = personalProfile ?? this.personalProfile
      ..proportion = proportion ?? this.proportion
      ..certificate = certificate ?? this.certificate
      ..skill = skill ?? this.skill
      ..isDefault = isDefault ?? this.isDefault
      ..integral = integral ?? this.integral
      ..phone = phone ?? this.phone
      ..email = email ?? this.email
      ..userId = userId ?? this.userId
      ..releaseTime = releaseTime ?? this.releaseTime
      ..labels = labels ?? this.labels
      ..mapLocation = mapLocation ?? this.mapLocation
      ..street = street ?? this.street
      ..jobSeekerGroupCode = jobSeekerGroupCode ?? this.jobSeekerGroupCode
      ..selectCount = selectCount ?? this.selectCount;
  }
}

ResumeWorkExperience $ResumeWorkExperienceFromJson(Map<String, dynamic> json) {
  final ResumeWorkExperience resumeWorkExperience = ResumeWorkExperience();
  final String? jobCompany = jsonConvert.convert<String>(json['JobCompany']);
  if (jobCompany != null) {
    resumeWorkExperience.jobCompany = jobCompany;
  }
  final String? jobName = jsonConvert.convert<String>(json['JobName']);
  if (jobName != null) {
    resumeWorkExperience.jobName = jobName;
  }
  final String? jobinTime = jsonConvert.convert<String>(json['JobinTime']);
  if (jobinTime != null) {
    resumeWorkExperience.jobinTime = jobinTime;
  }
  final String? joboutTime = jsonConvert.convert<String>(json['JoboutTime']);
  if (joboutTime != null) {
    resumeWorkExperience.joboutTime = joboutTime;
  }
  final String? jobDetail = jsonConvert.convert<String>(json['JobDetail']);
  if (jobDetail != null) {
    resumeWorkExperience.jobDetail = jobDetail;
  }
  return resumeWorkExperience;
}

Map<String, dynamic> $ResumeWorkExperienceToJson(ResumeWorkExperience entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['JobCompany'] = entity.jobCompany;
  data['JobName'] = entity.jobName;
  data['JobinTime'] = entity.jobinTime;
  data['JoboutTime'] = entity.joboutTime;
  data['JobDetail'] = entity.jobDetail;
  return data;
}

extension ResumeWorkExperienceExtension on ResumeWorkExperience {
  ResumeWorkExperience copyWith({
    String? jobCompany,
    String? jobName,
    String? jobinTime,
    String? joboutTime,
    String? jobDetail,
  }) {
    return ResumeWorkExperience()
      ..jobCompany = jobCompany ?? this.jobCompany
      ..jobName = jobName ?? this.jobName
      ..jobinTime = jobinTime ?? this.jobinTime
      ..joboutTime = joboutTime ?? this.joboutTime
      ..jobDetail = jobDetail ?? this.jobDetail;
  }
}

ResumeEduExperience $ResumeEduExperienceFromJson(Map<String, dynamic> json) {
  final ResumeEduExperience resumeEduExperience = ResumeEduExperience();
  final String? eduSchool = jsonConvert.convert<String>(json['EduSchool']);
  if (eduSchool != null) {
    resumeEduExperience.eduSchool = eduSchool;
  }
  final String? eduProfessional = jsonConvert.convert<String>(
      json['EduProfessional']);
  if (eduProfessional != null) {
    resumeEduExperience.eduProfessional = eduProfessional;
  }
  final String? eduLevelValue = jsonConvert.convert<String>(
      json['EduLevelValue']);
  if (eduLevelValue != null) {
    resumeEduExperience.eduLevelValue = eduLevelValue;
  }
  final String? eduinTime = jsonConvert.convert<String>(json['EduinTime']);
  if (eduinTime != null) {
    resumeEduExperience.eduinTime = eduinTime;
  }
  final String? eduoutTime = jsonConvert.convert<String>(json['EduoutTime']);
  if (eduoutTime != null) {
    resumeEduExperience.eduoutTime = eduoutTime;
  }
  return resumeEduExperience;
}

Map<String, dynamic> $ResumeEduExperienceToJson(ResumeEduExperience entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['EduSchool'] = entity.eduSchool;
  data['EduProfessional'] = entity.eduProfessional;
  data['EduLevelValue'] = entity.eduLevelValue;
  data['EduinTime'] = entity.eduinTime;
  data['EduoutTime'] = entity.eduoutTime;
  return data;
}

extension ResumeEduExperienceExtension on ResumeEduExperience {
  ResumeEduExperience copyWith({
    String? eduSchool,
    String? eduProfessional,
    String? eduLevelValue,
    String? eduinTime,
    String? eduoutTime,
  }) {
    return ResumeEduExperience()
      ..eduSchool = eduSchool ?? this.eduSchool
      ..eduProfessional = eduProfessional ?? this.eduProfessional
      ..eduLevelValue = eduLevelValue ?? this.eduLevelValue
      ..eduinTime = eduinTime ?? this.eduinTime
      ..eduoutTime = eduoutTime ?? this.eduoutTime;
  }
}