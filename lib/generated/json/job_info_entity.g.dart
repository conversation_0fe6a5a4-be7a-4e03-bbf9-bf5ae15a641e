import 'package:flutter_kit/generated/json/base/json_convert_content.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';

JobInfoEntity $JobInfoEntityFromJson(Map<String, dynamic> json) {
  final JobInfoEntity jobInfoEntity = JobInfoEntity();
  final String? id = jsonConvert.convert<String>(json['Id']);
  if (id != null) {
    jobInfoEntity.id = id;
  }
  final String? enterpriseId = jsonConvert.convert<String>(
      json['EnterpriseId']);
  if (enterpriseId != null) {
    jobInfoEntity.enterpriseId = enterpriseId;
  }
  final String? enterpriseName = jsonConvert.convert<String>(
      json['EnterpriseName']);
  if (enterpriseName != null) {
    jobInfoEntity.enterpriseName = enterpriseName;
  }
  final List<JobInfoUserAuths>? userAuths = (json['UserAuths'] as List<
      dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<JobInfoUserAuths>(e) as JobInfoUserAuths)
      .toList();
  if (userAuths != null) {
    jobInfoEntity.userAuths = userAuths;
  }
  final String? enterpriseLogoSmall = jsonConvert.convert<String>(
      json['EnterpriseLogoSmall']);
  if (enterpriseLogoSmall != null) {
    jobInfoEntity.enterpriseLogoSmall = enterpriseLogoSmall;
  }
  final String? name = jsonConvert.convert<String>(json['Name']);
  if (name != null) {
    jobInfoEntity.name = name;
  }
  final String? nature = jsonConvert.convert<String>(json['Nature']);
  if (nature != null) {
    jobInfoEntity.nature = nature;
  }
  final String? jobTypeId = jsonConvert.convert<String>(json['JobTypeId']);
  if (jobTypeId != null) {
    jobInfoEntity.jobTypeId = jobTypeId;
  }
  final String? jobTypeName = jsonConvert.convert<String>(json['JobTypeName']);
  if (jobTypeName != null) {
    jobInfoEntity.jobTypeName = jobTypeName;
  }
  final String? department = jsonConvert.convert<String>(json['Department']);
  if (department != null) {
    jobInfoEntity.department = department;
  }
  final int? recruitingCount = jsonConvert.convert<int>(
      json['RecruitingCount']);
  if (recruitingCount != null) {
    jobInfoEntity.recruitingCount = recruitingCount;
  }
  final String? payWay = jsonConvert.convert<String>(json['PayWay']);
  if (payWay != null) {
    jobInfoEntity.payWay = payWay;
  }
  final String? workAreaId = jsonConvert.convert<String>(json['WorkAreaId']);
  if (workAreaId != null) {
    jobInfoEntity.workAreaId = workAreaId;
  }
  final String? workAreaName = jsonConvert.convert<String>(
      json['WorkAreaName']);
  if (workAreaName != null) {
    jobInfoEntity.workAreaName = workAreaName;
  }
  final String? workAreaCascadeName = jsonConvert.convert<String>(
      json['WorkAreaCascadeName']);
  if (workAreaCascadeName != null) {
    jobInfoEntity.workAreaCascadeName = workAreaCascadeName;
  }
  final String? pay = jsonConvert.convert<String>(json['Pay']);
  if (pay != null) {
    jobInfoEntity.pay = pay;
  }
  final String? jobPayUnit = jsonConvert.convert<String>(json['JobPayUnit']);
  if (jobPayUnit != null) {
    jobInfoEntity.jobPayUnit = jobPayUnit;
  }
  final String? welfare = jsonConvert.convert<String>(json['Welfare']);
  if (welfare != null) {
    jobInfoEntity.welfare = welfare;
  }
  final String? welfareValue = jsonConvert.convert<String>(
      json['WelfareValue']);
  if (welfareValue != null) {
    jobInfoEntity.welfareValue = welfareValue;
  }
  final String? mapLocation = jsonConvert.convert<String>(json['MapLocation']);
  if (mapLocation != null) {
    jobInfoEntity.mapLocation = mapLocation;
  }
  final String? street = jsonConvert.convert<String>(json['Street']);
  if (street != null) {
    jobInfoEntity.street = street;
  }
  final String? demandEducationCode = jsonConvert.convert<String>(
      json['DemandEducationCode']);
  if (demandEducationCode != null) {
    jobInfoEntity.demandEducationCode = demandEducationCode;
  }
  final String? workAddress = jsonConvert.convert<String>(json['WorkAddress']);
  if (workAddress != null) {
    jobInfoEntity.workAddress = workAddress;
  }
  final String? releaseTime = jsonConvert.convert<String>(json['ReleaseTime']);
  if (releaseTime != null) {
    jobInfoEntity.releaseTime = releaseTime;
  }
  final bool? isPutaway = jsonConvert.convert<bool>(json['IsPutaway']);
  if (isPutaway != null) {
    jobInfoEntity.isPutaway = isPutaway;
  }
  final int? applyCount = jsonConvert.convert<int>(json['ApplyCount']);
  if (applyCount != null) {
    jobInfoEntity.applyCount = applyCount;
  }
  final int? selectCount = jsonConvert.convert<int>(json['SelectCount']);
  if (selectCount != null) {
    jobInfoEntity.selectCount = selectCount;
  }
  final String? releaseChannel = jsonConvert.convert<String>(
      json['ReleaseChannel']);
  if (releaseChannel != null) {
    jobInfoEntity.releaseChannel = releaseChannel;
  }
  return jobInfoEntity;
}

Map<String, dynamic> $JobInfoEntityToJson(JobInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Id'] = entity.id;
  data['EnterpriseId'] = entity.enterpriseId;
  data['EnterpriseName'] = entity.enterpriseName;
  data['UserAuths'] = entity.userAuths.map((v) => v.toJson()).toList();
  data['EnterpriseLogoSmall'] = entity.enterpriseLogoSmall;
  data['Name'] = entity.name;
  data['Nature'] = entity.nature;
  data['JobTypeId'] = entity.jobTypeId;
  data['JobTypeName'] = entity.jobTypeName;
  data['Department'] = entity.department;
  data['RecruitingCount'] = entity.recruitingCount;
  data['PayWay'] = entity.payWay;
  data['WorkAreaId'] = entity.workAreaId;
  data['WorkAreaName'] = entity.workAreaName;
  data['WorkAreaCascadeName'] = entity.workAreaCascadeName;
  data['Pay'] = entity.pay;
  data['JobPayUnit'] = entity.jobPayUnit;
  data['Welfare'] = entity.welfare;
  data['WelfareValue'] = entity.welfareValue;
  data['MapLocation'] = entity.mapLocation;
  data['Street'] = entity.street;
  data['DemandEducationCode'] = entity.demandEducationCode;
  data['WorkAddress'] = entity.workAddress;
  data['ReleaseTime'] = entity.releaseTime;
  data['IsPutaway'] = entity.isPutaway;
  data['ApplyCount'] = entity.applyCount;
  data['SelectCount'] = entity.selectCount;
  data['ReleaseChannel'] = entity.releaseChannel;
  return data;
}

extension JobInfoEntityExtension on JobInfoEntity {
  JobInfoEntity copyWith({
    String? id,
    String? enterpriseId,
    String? enterpriseName,
    List<JobInfoUserAuths>? userAuths,
    String? enterpriseLogoSmall,
    String? name,
    String? nature,
    String? jobTypeId,
    String? jobTypeName,
    String? department,
    int? recruitingCount,
    String? payWay,
    String? workAreaId,
    String? workAreaName,
    String? workAreaCascadeName,
    String? pay,
    String? jobPayUnit,
    String? welfare,
    String? welfareValue,
    String? mapLocation,
    String? street,
    String? demandEducationCode,
    String? workAddress,
    String? releaseTime,
    bool? isPutaway,
    int? applyCount,
    int? selectCount,
    String? releaseChannel,
  }) {
    return JobInfoEntity()
      ..id = id ?? this.id
      ..enterpriseId = enterpriseId ?? this.enterpriseId
      ..enterpriseName = enterpriseName ?? this.enterpriseName
      ..userAuths = userAuths ?? this.userAuths
      ..enterpriseLogoSmall = enterpriseLogoSmall ?? this.enterpriseLogoSmall
      ..name = name ?? this.name
      ..nature = nature ?? this.nature
      ..jobTypeId = jobTypeId ?? this.jobTypeId
      ..jobTypeName = jobTypeName ?? this.jobTypeName
      ..department = department ?? this.department
      ..recruitingCount = recruitingCount ?? this.recruitingCount
      ..payWay = payWay ?? this.payWay
      ..workAreaId = workAreaId ?? this.workAreaId
      ..workAreaName = workAreaName ?? this.workAreaName
      ..workAreaCascadeName = workAreaCascadeName ?? this.workAreaCascadeName
      ..pay = pay ?? this.pay
      ..jobPayUnit = jobPayUnit ?? this.jobPayUnit
      ..welfare = welfare ?? this.welfare
      ..welfareValue = welfareValue ?? this.welfareValue
      ..mapLocation = mapLocation ?? this.mapLocation
      ..street = street ?? this.street
      ..demandEducationCode = demandEducationCode ?? this.demandEducationCode
      ..workAddress = workAddress ?? this.workAddress
      ..releaseTime = releaseTime ?? this.releaseTime
      ..isPutaway = isPutaway ?? this.isPutaway
      ..applyCount = applyCount ?? this.applyCount
      ..selectCount = selectCount ?? this.selectCount
      ..releaseChannel = releaseChannel ?? this.releaseChannel;
  }
}

JobInfoUserAuths $JobInfoUserAuthsFromJson(Map<String, dynamic> json) {
  final JobInfoUserAuths jobInfoUserAuths = JobInfoUserAuths();
  final String? type = jsonConvert.convert<String>(json['Type']);
  if (type != null) {
    jobInfoUserAuths.type = type;
  }
  final String? addTime = jsonConvert.convert<String>(json['AddTime']);
  if (addTime != null) {
    jobInfoUserAuths.addTime = addTime;
  }
  return jobInfoUserAuths;
}

Map<String, dynamic> $JobInfoUserAuthsToJson(JobInfoUserAuths entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['Type'] = entity.type;
  data['AddTime'] = entity.addTime;
  return data;
}

extension JobInfoUserAuthsExtension on JobInfoUserAuths {
  JobInfoUserAuths copyWith({
    String? type,
    String? addTime,
  }) {
    return JobInfoUserAuths()
      ..type = type ?? this.type
      ..addTime = addTime ?? this.addTime;
  }
}