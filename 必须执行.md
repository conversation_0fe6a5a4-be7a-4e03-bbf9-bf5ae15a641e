# 必须执行的工作规则

## 核心原则

### 1. 不创建示例代码
- 不需要创建使用示例
- 不需要创建演示代码
- 专注于实际功能实现

### 2. 不创建文档文件
- 不需要创建 MD 文件（除非明确要求）
- 不需要创建说明文档
- 不需要创建 README 文件

### 3. 任务管理流程
- **必须先创建任务列表**
- **不要自动执行任务**
- **等待明确指示才执行**
- 使用任务管理工具组织工作

## 工作流程

1. 分析用户需求
2. 创建详细任务列表
3. 等待用户指示
4. 按指示执行具体任务
5. 更新任务状态

## 重要提醒

**每次对话开始时都要遵循这些规则，无例外。**


## 脚本命令
### 更新 app_rouer.gr.dart 
- dart run build_runner build






## 项目架构

### 技术栈选型
```
核心框架: Flutter 3.0+
状态管理: Provider + ChangeNotifier
路由管理: AutoRoute
依赖注入: GetIt
网络请求: Dio
本地存储: SharedPreferences
屏幕适配: flutter_screenutil
国际化: flutter_localizations + intl
代码生成: build_runner + json_serializable
```

### 采用架构方案

### 整体架构设计

#### 分层架构
```
┌─────────────────────────────────────┐
│           Presentation Layer        │  UI层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │   Pages     │ │   Widgets       │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│            Business Layer           │  业务层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ Controllers │ │  View Models    │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│             Data Layer              │  数据层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │Repositories │ │   Data Sources  │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│            Core Layer               │  核心层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │   Services  │ │    Utilities    │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘


## 📁 目前正在使用的架构和模块

### 目录结构
```
lib/
├── src/
│   ├── base/                    # 基础架构层 ✅
│   │   ├── core/               # 核心状态定义
│   │   │   ├── view_state.dart          ✅ 已实现
│   │   │   ├── base_result.dart         ✅ 已实现
│   │   │   └── exceptions.dart          🔄 待完善
│   │   ├── logic/              # 业务逻辑基类
│   │   │   ├── view_state_logic.dart    ✅ 已实现
│   │   │   └── view_state_paging_logic.dart ✅ 已实现
│   │   ├── widgets/            # UI组件基类
│   │   │   ├── view_state_widget.dart   ✅ 已重构
│   │   │   ├── state_widget_builder.dart ✅ 已实现
│   │   │   └── view_state_paging_widget.dart ✅ 已实现
│   │   └── l10n/               # 国际化支持
│   │       └── base_localizations.dart  ✅ 已实现
│   ├── shared/                 # 共享组件层
│   │   ├── components/         # 通用UI组件
│   │   │   ├── app_bars/       # AppBar组件 🔄 待完善
│   │   │   ├── buttons/        # 按钮组件 ❌ 待实现
│   │   │   ├── forms/          # 表单组件 🔄 部分实现
│   │   │   └── cards/          # 卡片组件 ❌ 待实现
│   │   ├── extensions/         # 扩展方法 🔄 部分实现
│   │   ├── services/           # 共享服务
│   │   │   ├── storage/        ✅ 已实现
│   │   │   ├── logger/         ✅ 已实现
│   │   │   └── analytics/      ❌ 待实现
│   │   └── locator.dart        # 依赖注入配置 ✅ 已重构
│   ├── core/                   # 核心配置层
│   │   ├── theme/              # 主题配置 🔄 部分实现
│   │   ├── routing/            # 路由配置 ✅ 已实现
│   │   ├── application.dart    # 应用入口 ✅ 已实现
│   │   └── app_initializer.dart # 初始化配置 ✅ 已实现
│   ├── datasource/             # 数据源层
│   │   ├── http/               # 网络请求 ✅ 已实现
│   │   ├── models/             # 数据模型 🔄 部分实现
│   │   ├── repositories/       # 数据仓库 ✅ 已实现
│   │   └── local/              # 本地数据源 ❌ 待实现
│   └── features/               # 功能模块层
│       ├── home/               # 首页模块 ✅ 已实现
│       ├── login/              # 登录模块 🔄 部分实现
│       ├── jobDetail/          # 职位详情 🔄 部分实现
│       └── [其他模块]           # 待扩展
└── main.dart                   # 应用入口 ✅ 已实现
```



## 使用文档
### 创建xx模块
1、创建screen 需要继承...
2、创建.... 需要。。。。