# Flutter Kit 开发架构规范

## 1. 总体架构原则

### 1.1 设计理念
- **组合优于继承**: 使用组合模式提高代码灵活性
- **单一职责**: 每个类只负责一个功能
- **依赖注入**: 通过构造函数注入依赖，便于测试
- **状态管理统一**: 使用统一的状态管理模式
- **代码复用**: 通过基础组件和工具类减少重复代码

### 1.2 目录结构规范
```
lib/
├── src/
│   ├── base/                    # 基础架构层
│   │   ├── core/               # 核心状态定义
│   │   ├── logic/              # 业务逻辑基类
│   │   ├── widgets/            # UI组件基类
│   │   └── l10n/               # 国际化支持
│   ├── shared/                 # 共享组件层
│   │   ├── components/         # 通用UI组件
│   │   │   ├── app_bars/       # AppBar组件
│   │   │   └── base/           # 基础页面组件
│   │   ├── extensions/         # 扩展方法
│   │   └── utils/              # 工具类
│   ├── core/                   # 核心配置层
│   │   ├── theme/              # 主题配置
│   │   ├── constants/          # 常量定义
│   │   └── config/             # 应用配置
│   ├── datasource/             # 数据源层
│   │   ├── http/               # 网络请求
│   │   ├── models/             # 数据模型
│   │   └── repositories/       # 数据仓库
│   └── features/               # 功能模块层
│       └── [feature_name]/
│           ├── ui/             # 页面UI
│           ├── logic/          # 业务逻辑
│           └── models/         # 模块专用模型
```

## 2. 页面开发规范

### 2.1 页面基类使用
**强制要求**: 所有页面必须继承 `ViewStateWidget`

```dart
class HomePage extends ViewStateWidget<HomeController> {
  const HomePage({super.key});

  @override
  HomeController get logic => HomeController();

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context) {
    return CustomAppBar(
      config: AppBarConfig.defaultConfig(title: '首页'),
    );
  }

  @override
  Widget buildBody(BuildContext context) {
    return const HomeContent();
  }
}
```

### 2.2 业务逻辑层规范
**强制要求**: 所有业务逻辑必须继承对应的基类

#### 2.2.1 普通页面逻辑
```dart
class HomeController extends ViewStateLogic<HomeData> {
  final HomeRepository repository;
  
  HomeController({required this.repository});

  @override
  Future<BaseResult<HomeData>> fetchData() async {
    try {
      final result = await repository.getHomeData();
      return BaseResult(data: result, code: 200, msg: 'success');
    } catch (e) {
      return BaseResult(code: -1, msg: e.toString());
    }
  }
}
```

#### 2.2.2 分页列表逻辑
```dart
class JobListController extends ViewStatePagingLogic<JobInfoEntity> {
  final JobRepository repository;
  
  JobListController({required this.repository}) {
    pageSize = 20;
  }

  @override
  Future<BaseResult<List<JobInfoEntity>>> fetchData(int page) async {
    try {
      final result = await repository.getJobList(page, pageSize);
      return BaseResult(data: result, code: 200, msg: 'success');
    } catch (e) {
      return BaseResult(data: <JobInfoEntity>[], code: -1, msg: e.toString());
    }
  }
}
```

## 3. AppBar 规范

### 3.1 AppBar 类型定义
**强制要求**: 只能使用以下4种AppBar类型

#### 3.1.1 默认AppBar（带返回按钮和居中标题）
```dart
@override
PreferredSizeWidget? buildAppBar(BuildContext context) {
  return CustomAppBar(
    config: AppBarConfig.defaultConfig(
      title: '页面标题',
      actions: [IconButton(...)], // 可选
    ),
  );
}
```

#### 3.1.2 自定义AppBar（支持单页面自定义视图）
```dart
@override
PreferredSizeWidget? buildAppBar(BuildContext context) {
  return CustomAppBar(
    config: AppBarConfig.custom(
      customWidget: CustomTitleWidget(),
      backgroundColor: Colors.transparent,
    ),
  );
}
```

#### 3.1.3 左返回+居中标题+右图标
```dart
@override
PreferredSizeWidget? buildAppBar(BuildContext context) {
  return CustomAppBar(
    config: AppBarConfig.withRightIcon(
      title: '页面标题',
      rightIcon: Icons.search,
      onRightIconTap: () => _handleSearch(),
    ),
  );
}
```

#### 3.1.4 透明AppBar（滚动时显示）
```dart
@override
PreferredSizeWidget? buildAppBar(BuildContext context) {
  return CustomAppBar(
    config: AppBarConfig.transparent(
      title: '页面标题',
      showOnScroll: true,
    ),
  );
}
```

### 3.2 禁止事项
- **禁止**: 直接使用 `AppBar` 组件
- **禁止**: 自定义AppBar样式（必须使用统一配置）
- **禁止**: 在页面中硬编码AppBar属性

## 4. 状态管理规范

### 4.1 状态定义
**强制要求**: 使用统一的状态枚举

```dart
// 正确示例
if (controller.viewState.isLoading()) {
  return LoadingWidget();
}

if (controller.viewState.isSuccess()) {
  return SuccessContent();
}

// 错误示例 - 禁止
if (controller.isLoading) { ... }
```

### 4.2 数据更新
**强制要求**: 通过基类方法更新状态

```dart
// 正确示例
class HomeController extends ViewStateLogic<HomeData> {
  void updateData() {
    setLoading();
    fetchData().then((result) {
      if (result.isSuccess()) {
        setSuccess(result.data);
      } else {
        setError(result.msg);
      }
    });
  }
}

// 错误示例 - 禁止直接操作状态
controller.viewState = ViewStateSuccess(data);
```

## 5. 网络请求规范

### 5.1 Repository 模式
**强制要求**: 所有网络请求必须通过Repository层

```dart
class HomeRepository {
  final ApiService _apiService;
  
  HomeRepository(this._apiService);

  Future<List<JobInfoEntity>> getJobList(int page, int pageSize) async {
    final response = await _apiService.getJobList(page, pageSize);
    if (response is ApiResponseSuccess) {
      final apiResult = response.response;
      if (apiResult.isSuccess()) {
        return apiResult.data as List<JobInfoEntity>;
      }
    }
    throw Exception('Failed to load job list');
  }
}
```

### 5.2 错误处理
**强制要求**: 统一错误处理机制

```dart
try {
  final result = await repository.getData();
  return BaseResult(data: result, code: 200, msg: 'success');
} on NetworkException catch (e) {
  return BaseResult(code: -1, msg: '网络错误: ${e.message}');
} on BusinessException catch (e) {
  return BaseResult(code: e.code, msg: e.message);
} catch (e) {
  return BaseResult(code: -1, msg: '未知错误: $e');
}
```

## 6. UI组件规范

### 6.1 列表组件
**强制要求**: 分页列表使用 `SimplePagingListView`

```dart
SimplePagingListView<JobInfoEntity>(
  logic: controller,
  itemBuilder: (context, job, index) {
    return JobListItem(item: job, index: index);
  },
)
```

### 6.2 状态组件
**强制要求**: 使用统一的状态组件

```dart
// 加载状态
StateWidgetBuilder.loading(context: context)

// 错误状态
StateWidgetBuilder.error(
  context: context,
  message: '加载失败',
  onRetry: () => controller.loadData(),
)

// 空数据状态
StateWidgetBuilder.empty(
  context: context,
  onRefresh: () => controller.loadData(),
)
```

## 7. 依赖注入规范

### 7.1 构造函数注入
**强制要求**: 通过构造函数注入依赖

```dart
// 正确示例
class HomeController extends ViewStateLogic<HomeData> {
  final HomeRepository repository;
  final UserService userService;
  
  HomeController({
    required this.repository,
    required this.userService,
  });
}

// 错误示例 - 禁止
class HomeController extends ViewStateLogic<HomeData> {
  late final HomeRepository repository;
  
  HomeController() {
    repository = HomeRepository(); // 禁止在构造函数中创建依赖
  }
}
```
