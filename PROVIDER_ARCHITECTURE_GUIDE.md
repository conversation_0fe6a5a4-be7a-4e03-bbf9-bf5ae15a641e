# Provider架构使用指南

## 🎯 概述

本指南展示如何正确使用基于Provider的Flutter Kit架构，避免GetX依赖，实现统一的状态管理和UI组件。

## 🏗️ 架构核心组件

### 1. ViewStateLogic - 业务逻辑基类
```dart
// 继承ChangeNotifier，与Provider完美集成
abstract class ViewStateLogic extends ChangeNotifier {
  ViewState _viewState = ViewState();
  
  // 便捷的状态设置方法
  void setLoading() => viewState = ViewStateLoading();
  void setSuccess<T>(T data) => viewState = ViewStateSuccess(data);
  void setError(String message) => viewState = ViewStateError(-1, message);
  // ...
}
```

### 2. ViewStateWidget - 页面基类
```dart
// 自动集成Provider，处理状态切换
abstract class ViewStateWidget<T extends ViewStateLogic> extends StatelessWidget {
  // 子类实现Controller创建
  T createController();
  
  // 子类实现页面内容
  Widget buildBody(BuildContext context, T logic);
  
  // 自动处理Provider包装和状态切换
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<T>(
      create: (_) => createController()..loadData(),
      child: Consumer<T>(
        builder: (context, logic, child) {
          // 自动状态切换逻辑
        },
      ),
    );
  }
}
```

### 3. 统一依赖注入
```dart
// lib/src/shared/locator.dart
void setupLocator() {
  // 核心服务
  locator.registerLazySingleton(() => ApiService());
  
  // Repository层
  locator.registerLazySingleton(() => HomeRepository());
  
  // Controller层 - 工厂模式
  locator.registerFactory(() => HomeController(
    repository: locator<HomeRepository>()
  ));
}
```

## 📝 正确使用示例

### 1. Controller实现
```dart
class HomeController extends ViewStatePagingLogic {
  final HomeRepository repository;
  List<JobInfoEntity> _jobs = [];

  List<JobInfoEntity> get jobs => _jobs;

  HomeController({required this.repository}) {
    pageSize = 20;
  }

  @override
  void loadData() {
    refreshPaging();
  }

  @override
  void refreshPaging() {
    sendRefreshPagingRequest(_fetchJobs(1), successBlock: (data) {
      _jobs = data;
      notifyListeners(); // 通知UI更新
    });
  }

  @override
  void loadMorePaging() {
    sendLoadMorePagingRequest(_fetchJobs(curPage + 1), successBlock: (data) {
      _jobs.addAll(data);
      notifyListeners();
    });
  }

  Future<BaseResult<List<JobInfoEntity>>> _fetchJobs(int page) async {
    try {
      final response = await repository.getHomeList(page, pageSize);
      // 处理响应...
      return BaseResult(data: jobList, code: 200, msg: 'success');
    } catch (e) {
      return BaseResult(data: <JobInfoEntity>[], code: -1, msg: e.toString());
    }
  }
}
```

### 2. 页面实现
```dart
@RoutePage()
class HomeScreen extends ViewStateWidget<HomeController> {
  const HomeScreen({super.key});

  @override
  HomeController createController() {
    return locator<HomeController>(); // 从依赖注入获取
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, HomeController logic) {
    return AppBar(
      title: Text('首页'),
      backgroundColor: Colors.transparent,
    );
  }

  @override
  Widget buildBody(BuildContext context, HomeController logic) {
    return RefreshIndicator(
      onRefresh: () async => logic.refreshPaging(),
      child: ListView.builder(
        itemCount: logic.jobs.length,
        itemBuilder: (context, index) {
          return JobListItem(
            item: logic.jobs[index],
            index: index,
          );
        },
      ),
    );
  }
}
```

### 3. 路由集成
```dart
// 使用AutoRoute，无需额外的Provider包装
@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> routes = [
    AutoRoute(page: HomeRoute.page, initial: true),
    // ViewStateWidget自动处理Provider
  ];
}
```

## 🔄 状态管理流程

### 1. 自动状态切换
```dart
// ViewStateWidget自动处理以下状态：
if (viewState.isLoading()) {
  return StateWidgetBuilder.loading(context: context);
} else if (viewState.isError()) {
  return StateWidgetBuilder.error(
    context: context,
    message: viewState.errorMessage,
    onRetry: () => logic.retry(),
  );
} else if (viewState.isEmpty()) {
  return StateWidgetBuilder.empty(
    context: context,
    onRefresh: () => logic.refresh(),
  );
} else if (viewState.isSuccess()) {
  return buildBody(context, logic); // 显示实际内容
}
```

### 2. 手动状态控制
```dart
class HomeController extends ViewStateLogic {
  void loadUserData() {
    setLoading(); // 显示加载状态
    
    repository.getUserData().then((result) {
      if (result.isSuccess()) {
        setSuccess(result.data); // 显示成功内容
      } else {
        setError(result.msg); // 显示错误状态
      }
    }).catchError((e) {
      setError('网络错误: $e'); // 显示网络错误
    });
  }
}
```

## 🎨 UI组件使用

### 1. 状态组件
```dart
// 加载状态
StateWidgetBuilder.loading(context: context)

// 错误状态
StateWidgetBuilder.error(
  context: context,
  message: '加载失败',
  onRetry: () => controller.loadData(),
)

// 空数据状态
StateWidgetBuilder.empty(
  context: context,
  onRefresh: () => controller.loadData(),
)
```

### 2. 分页列表
```dart
// 使用ViewStatePagingWidget（如果需要）
class JobListPage extends ViewStateWidget<JobListController> {
  @override
  Widget buildBody(BuildContext context, JobListController logic) {
    return SimplePagingListView<JobInfoEntity>(
      logic: logic,
      itemBuilder: (context, job, index) {
        return JobListItem(item: job, index: index);
      },
    );
  }
}
```

## ✅ 最佳实践

### 1. 依赖注入
- ✅ 使用GetIt进行依赖注入
- ✅ Repository层使用单例
- ✅ Controller层使用工厂模式
- ❌ 避免在Widget中直接创建依赖

### 2. 状态管理
- ✅ 使用ViewStateLogic基类
- ✅ 通过便捷方法设置状态
- ✅ 在数据变化时调用notifyListeners()
- ❌ 避免直接操作viewState

### 3. UI组件
- ✅ 继承ViewStateWidget
- ✅ 使用统一的状态组件
- ✅ 实现createController方法
- ❌ 避免手动处理Provider包装

### 4. 错误处理
- ✅ 使用统一的错误处理机制
- ✅ 区分网络错误和业务错误
- ✅ 提供重试功能
- ❌ 避免忽略错误状态

## 🚀 迁移指南

### 从当前架构迁移：
1. 替换StatefulWidget → ViewStateWidget
2. 移除手动Provider包装
3. 使用locator获取Controller
4. 实现createController方法
5. 更新buildBody方法签名

### 示例迁移：
```dart
// 旧代码
class HomeScreen extends StatefulWidget implements AutoRouteWrapper {
  @override
  Widget wrappedRoute(BuildContext context) {
    return ChangeNotifierProvider<HomeController>(
      create: (_) => HomeController(repository: HomeRepository()),
      child: this,
    );
  }
}

// 新代码
class HomeScreen extends ViewStateWidget<HomeController> {
  @override
  HomeController createController() => locator<HomeController>();
  
  @override
  Widget buildBody(BuildContext context, HomeController logic) {
    // 页面内容
  }
}
```

## 📚 总结

这个基于Provider的架构提供了：
- 🎯 统一的状态管理
- 🔄 自动的状态切换
- 💉 清晰的依赖注入
- 🎨 一致的UI组件
- 🧪 易于测试的结构

通过遵循这些模式，你可以构建出结构清晰、易于维护的Flutter应用。
